{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ScholarshipsByLevel = () => {\n  _s();\n  const {\n    level\n  } = useParams();\n  const {\n    translations\n  } = useLanguage();\n  const [scholarships, setScholarships] = useState([]);\n  const [allLevels, setAllLevels] = useState([]);\n  const [latestScholarships, setLatestScholarships] = useState([]);\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const decodedLevel = level ? decodeURIComponent(level) : '';\n  useEffect(() => {\n    if (decodedLevel) {\n      fetchScholarshipsByLevel();\n      fetchAllLevels();\n      fetchLatestScholarships();\n      fetchLevelStatistics();\n    }\n  }, [decodedLevel, currentPage]);\n  const fetchScholarshipsByLevel = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/scholarships?level=${encodeURIComponent(decodedLevel)}&page=${currentPage}&limit=12`);\n      if (response.ok) {\n        var _data$pagination;\n        const data = await response.json();\n        setScholarships(data.data || []);\n        setTotalPages(((_data$pagination = data.pagination) === null || _data$pagination === void 0 ? void 0 : _data$pagination.totalPages) || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships by level:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchAllLevels = async () => {\n    try {\n      const response = await fetch('/api/scholarships/levels');\n      if (response.ok) {\n        var _data$data;\n        const data = await response.json();\n        setAllLevels(((_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.map(item => item.name)) || []);\n      }\n    } catch (error) {\n      console.error('Error fetching levels:', error);\n    }\n  };\n  const fetchLatestScholarships = async () => {\n    try {\n      const response = await fetch('/api/scholarships?limit=6&orderBy=created_at&orderDirection=DESC');\n      if (response.ok) {\n        const data = await response.json();\n        setLatestScholarships(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching latest scholarships:', error);\n    }\n  };\n  const fetchLevelStatistics = async () => {\n    try {\n      const response = await fetch(`/api/scholarships/levels/statistics?level=${encodeURIComponent(decodedLevel)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching level statistics:', error);\n    }\n  };\n  const handleScholarshipClick = id => {\n    window.location.href = `/scholarships/${id}`;\n  };\n  const getLevelIcon = levelName => {\n    const icons = {\n      'licence': '🎓',\n      'bachelor': '🎓',\n      'undergraduate': '🎓',\n      'master': '🎯',\n      'masters': '🎯',\n      'graduate': '🎯',\n      'doctorat': '🔬',\n      'doctorate': '🔬',\n      'phd': '🔬',\n      'postdoc': '🧑‍🔬'\n    };\n    return icons[levelName.toLowerCase()] || '📚';\n  };\n  const getLevelColor = levelName => {\n    const colors = {\n      'licence': 'blue',\n      'bachelor': 'blue',\n      'undergraduate': 'blue',\n      'master': 'purple',\n      'masters': 'purple',\n      'graduate': 'purple',\n      'doctorat': 'indigo',\n      'doctorate': 'indigo',\n      'phd': 'indigo',\n      'postdoc': 'green'\n    };\n    return colors[levelName.toLowerCase()] || 'gray';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-gradient-to-r from-${getLevelColor(decodedLevel)}-600 to-${getLevelColor(decodedLevel)}-700 text-white py-16`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-6\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/scholarships\",\n            className: `flex items-center text-${getLevelColor(decodedLevel)}-200 hover:text-white transition-colors duration-200`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-5 w-5 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 19l-7-7 7-7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), \"Retour aux bourses\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-6xl mr-4\",\n            children: getLevelIcon(decodedLevel)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl md:text-5xl font-bold mb-2\",\n              children: [\"Bourses de \", decodedLevel]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xl text-${getLevelColor(decodedLevel)}-100`,\n              children: \"D\\xE9couvrez toutes les opportunit\\xE9s pour ce niveau d'\\xE9tudes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), statistics && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white py-12 border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `text-3xl font-bold text-${getLevelColor(decodedLevel)}-600 mb-2`,\n              children: statistics.totalScholarships\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Total des bourses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-green-600 mb-2\",\n              children: statistics.openScholarships\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Bourses ouvertes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-red-600 mb-2\",\n              children: statistics.closedScholarships\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Bourses ferm\\xE9es\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-2/3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: [\"Bourses de \", decodedLevel, \" (\", scholarships.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [...Array(6)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"aspect-[16/9] bg-gray-200\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-5 bg-gray-200 rounded w-3/4 mb-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-1/2 mb-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-full mb-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-10 bg-gray-100 rounded w-full mt-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this) : scholarships.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-6xl mb-4\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Aucune bourse trouv\\xE9e\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Aucune bourse n'est disponible pour ce niveau d'\\xE9tudes actuellement.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: scholarships.map((scholarship, index) => /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n                id: scholarship.id,\n                title: scholarship.title,\n                thumbnail: scholarship.thumbnail,\n                deadline: scholarship.deadline,\n                isOpen: scholarship.isOpen,\n                level: scholarship.level,\n                country: scholarship.country,\n                onClick: handleScholarshipClick,\n                index: index\n              }, scholarship.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center mt-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [...Array(totalPages)].map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setCurrentPage(index + 1),\n                  className: `px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${currentPage === index + 1 ? `bg-${getLevelColor(decodedLevel)}-600 text-white` : 'bg-white text-gray-700 hover:bg-gray-100'}`,\n                  children: index + 1\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-1/3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Autres Niveaux d'\\xC9tudes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: allLevels.filter(l => l !== decodedLevel).map(levelName => /*#__PURE__*/_jsxDEV(Link, {\n                to: `/scholarships/level/${encodeURIComponent(levelName)}`,\n                className: \"flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl mr-3\",\n                  children: getLevelIcon(levelName)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900\",\n                  children: levelName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)]\n              }, levelName, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Derni\\xE8res Bourses\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: latestScholarships.slice(0, 5).map(scholarship => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleScholarshipClick(scholarship.id),\n                className: \"cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 text-sm mb-1 line-clamp-2\",\n                  children: scholarship.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600 mb-2\",\n                  children: [scholarship.country, \" \\u2022 \", scholarship.level]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${scholarship.isOpen ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: scholarship.isOpen ? 'Ouvert' : 'Fermé'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this)]\n              }, scholarship.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(ScholarshipsByLevel, \"FGoqJiNHWiZIefIe5lFSlY3Gxv4=\", false, function () {\n  return [useParams, useLanguage];\n});\n_c = ScholarshipsByLevel;\nexport default ScholarshipsByLevel;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipsByLevel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useLanguage", "EnhancedScholarshipCard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScholarshipsByLevel", "_s", "level", "translations", "scholarships", "setScholarships", "allLevels", "setAllLevels", "latestScholarships", "setLatestScholarships", "statistics", "setStatistics", "loading", "setLoading", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "decodedLevel", "decodeURIComponent", "fetchScholarshipsByLevel", "fetchAllLevels", "fetchLatestScholarships", "fetchLevelStatistics", "response", "fetch", "encodeURIComponent", "ok", "_data$pagination", "data", "json", "pagination", "error", "console", "_data$data", "map", "item", "name", "handleScholarshipClick", "id", "window", "location", "href", "getLevelIcon", "levelName", "icons", "toLowerCase", "getLevelColor", "colors", "className", "children", "to", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalScholarships", "openScholarships", "closedScholarships", "length", "Array", "_", "index", "scholarship", "title", "thumbnail", "deadline", "isOpen", "country", "onClick", "filter", "l", "slice", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  deadline: string;\n  isOpen: boolean;\n  country: string;\n  level: string;\n  description?: string;\n  financialBenefitsSummary?: string;\n  eligibilitySummary?: string;\n}\n\ninterface LevelStatistics {\n  level: string;\n  totalScholarships: number;\n  openScholarships: number;\n  closedScholarships: number;\n}\n\nconst ScholarshipsByLevel: React.FC = () => {\n  const { level } = useParams<{ level: string }>();\n  const { translations } = useLanguage();\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [allLevels, setAllLevels] = useState<string[]>([]);\n  const [latestScholarships, setLatestScholarships] = useState<Scholarship[]>([]);\n  const [statistics, setStatistics] = useState<LevelStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  const decodedLevel = level ? decodeURIComponent(level) : '';\n\n  useEffect(() => {\n    if (decodedLevel) {\n      fetchScholarshipsByLevel();\n      fetchAllLevels();\n      fetchLatestScholarships();\n      fetchLevelStatistics();\n    }\n  }, [decodedLevel, currentPage]);\n\n  const fetchScholarshipsByLevel = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/scholarships?level=${encodeURIComponent(decodedLevel)}&page=${currentPage}&limit=12`);\n      if (response.ok) {\n        const data = await response.json();\n        setScholarships(data.data || []);\n        setTotalPages(data.pagination?.totalPages || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships by level:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchAllLevels = async () => {\n    try {\n      const response = await fetch('/api/scholarships/levels');\n      if (response.ok) {\n        const data = await response.json();\n        setAllLevels(data.data?.map((item: any) => item.name) || []);\n      }\n    } catch (error) {\n      console.error('Error fetching levels:', error);\n    }\n  };\n\n  const fetchLatestScholarships = async () => {\n    try {\n      const response = await fetch('/api/scholarships?limit=6&orderBy=created_at&orderDirection=DESC');\n      if (response.ok) {\n        const data = await response.json();\n        setLatestScholarships(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching latest scholarships:', error);\n    }\n  };\n\n  const fetchLevelStatistics = async () => {\n    try {\n      const response = await fetch(`/api/scholarships/levels/statistics?level=${encodeURIComponent(decodedLevel)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching level statistics:', error);\n    }\n  };\n\n  const handleScholarshipClick = (id: number) => {\n    window.location.href = `/scholarships/${id}`;\n  };\n\n  const getLevelIcon = (levelName: string): string => {\n    const icons: { [key: string]: string } = {\n      'licence': '🎓',\n      'bachelor': '🎓',\n      'undergraduate': '🎓',\n      'master': '🎯',\n      'masters': '🎯',\n      'graduate': '🎯',\n      'doctorat': '🔬',\n      'doctorate': '🔬',\n      'phd': '🔬',\n      'postdoc': '🧑‍🔬'\n    };\n    return icons[levelName.toLowerCase()] || '📚';\n  };\n\n  const getLevelColor = (levelName: string): string => {\n    const colors: { [key: string]: string } = {\n      'licence': 'blue',\n      'bachelor': 'blue',\n      'undergraduate': 'blue',\n      'master': 'purple',\n      'masters': 'purple',\n      'graduate': 'purple',\n      'doctorat': 'indigo',\n      'doctorate': 'indigo',\n      'phd': 'indigo',\n      'postdoc': 'green'\n    };\n    return colors[levelName.toLowerCase()] || 'gray';\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <div className={`bg-gradient-to-r from-${getLevelColor(decodedLevel)}-600 to-${getLevelColor(decodedLevel)}-700 text-white py-16`}>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center mb-6\">\n            <Link\n              to=\"/scholarships\"\n              className={`flex items-center text-${getLevelColor(decodedLevel)}-200 hover:text-white transition-colors duration-200`}\n            >\n              <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n              Retour aux bourses\n            </Link>\n          </div>\n          \n          <div className=\"flex items-center mb-4\">\n            <div className=\"text-6xl mr-4\">\n              {getLevelIcon(decodedLevel)}\n            </div>\n            <div>\n              <h1 className=\"text-4xl md:text-5xl font-bold mb-2\">\n                Bourses de {decodedLevel}\n              </h1>\n              <p className={`text-xl text-${getLevelColor(decodedLevel)}-100`}>\n                Découvrez toutes les opportunités pour ce niveau d'études\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Section */}\n      {statistics && (\n        <div className=\"bg-white py-12 border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div className=\"text-center\">\n                <div className={`text-3xl font-bold text-${getLevelColor(decodedLevel)}-600 mb-2`}>\n                  {statistics.totalScholarships}\n                </div>\n                <div className=\"text-gray-600\">Total des bourses</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600 mb-2\">\n                  {statistics.openScholarships}\n                </div>\n                <div className=\"text-gray-600\">Bourses ouvertes</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-red-600 mb-2\">\n                  {statistics.closedScholarships}\n                </div>\n                <div className=\"text-gray-600\">Bourses fermées</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Main Content Area */}\n          <div className=\"lg:w-2/3\">\n            <div className=\"mb-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Bourses de {decodedLevel} ({scholarships.length})\n              </h2>\n            </div>\n\n            {/* Scholarships Grid */}\n            {loading ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {[...Array(6)].map((_, index) => (\n                  <div key={index} className=\"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\">\n                    <div className=\"aspect-[16/9] bg-gray-200\"></div>\n                    <div className=\"p-6\">\n                      <div className=\"h-5 bg-gray-200 rounded w-3/4 mb-3\"></div>\n                      <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-3\"></div>\n                      <div className=\"h-4 bg-gray-200 rounded w-full mb-3\"></div>\n                      <div className=\"h-10 bg-gray-100 rounded w-full mt-4\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : scholarships.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <div className=\"text-6xl mb-4\">📚</div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                  Aucune bourse trouvée\n                </h3>\n                <p className=\"text-gray-600\">\n                  Aucune bourse n'est disponible pour ce niveau d'études actuellement.\n                </p>\n              </div>\n            ) : (\n              <>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {scholarships.map((scholarship, index) => (\n                    <EnhancedScholarshipCard\n                      key={scholarship.id}\n                      id={scholarship.id}\n                      title={scholarship.title}\n                      thumbnail={scholarship.thumbnail}\n                      deadline={scholarship.deadline}\n                      isOpen={scholarship.isOpen}\n                      level={scholarship.level}\n                      country={scholarship.country}\n                      onClick={handleScholarshipClick}\n                      index={index}\n                    />\n                  ))}\n                </div>\n\n                {/* Pagination */}\n                {totalPages > 1 && (\n                  <div className=\"flex justify-center mt-12\">\n                    <div className=\"flex space-x-2\">\n                      {[...Array(totalPages)].map((_, index) => (\n                        <button\n                          key={index}\n                          onClick={() => setCurrentPage(index + 1)}\n                          className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${\n                            currentPage === index + 1\n                              ? `bg-${getLevelColor(decodedLevel)}-600 text-white`\n                              : 'bg-white text-gray-700 hover:bg-gray-100'\n                          }`}\n                        >\n                          {index + 1}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"lg:w-1/3\">\n            {/* Other Levels */}\n            <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Autres Niveaux d'Études\n              </h3>\n              <div className=\"space-y-2\">\n                {allLevels.filter(l => l !== decodedLevel).map((levelName) => (\n                  <Link\n                    key={levelName}\n                    to={`/scholarships/level/${encodeURIComponent(levelName)}`}\n                    className=\"flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <span className=\"text-2xl mr-3\">{getLevelIcon(levelName)}</span>\n                    <span className=\"font-medium text-gray-900\">{levelName}</span>\n                  </Link>\n                ))}\n              </div>\n            </div>\n\n            {/* Latest Scholarships */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Dernières Bourses\n              </h3>\n              <div className=\"space-y-4\">\n                {latestScholarships.slice(0, 5).map((scholarship) => (\n                  <div\n                    key={scholarship.id}\n                    onClick={() => handleScholarshipClick(scholarship.id)}\n                    className=\"cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <h4 className=\"font-medium text-gray-900 text-sm mb-1 line-clamp-2\">\n                      {scholarship.title}\n                    </h4>\n                    <p className=\"text-xs text-gray-600 mb-2\">\n                      {scholarship.country} • {scholarship.level}\n                    </p>\n                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      scholarship.isOpen \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {scholarship.isOpen ? 'Ouvert' : 'Fermé'}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ScholarshipsByLevel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,uBAAuB,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsB5E,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM;IAAEC;EAAM,CAAC,GAAGV,SAAS,CAAoB,CAAC;EAChD,MAAM;IAAEW;EAAa,CAAC,GAAGT,WAAW,CAAC,CAAC;EACtC,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAW,EAAE,CAAC;EACxD,MAAM,CAACkB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnB,QAAQ,CAAgB,EAAE,CAAC;EAC/E,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAyB,IAAI,CAAC;EAC1E,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAM4B,YAAY,GAAGhB,KAAK,GAAGiB,kBAAkB,CAACjB,KAAK,CAAC,GAAG,EAAE;EAE3DX,SAAS,CAAC,MAAM;IACd,IAAI2B,YAAY,EAAE;MAChBE,wBAAwB,CAAC,CAAC;MAC1BC,cAAc,CAAC,CAAC;MAChBC,uBAAuB,CAAC,CAAC;MACzBC,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACL,YAAY,EAAEJ,WAAW,CAAC,CAAC;EAE/B,MAAMM,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,2BAA2BC,kBAAkB,CAACR,YAAY,CAAC,SAASJ,WAAW,WAAW,CAAC;MACxH,IAAIU,QAAQ,CAACG,EAAE,EAAE;QAAA,IAAAC,gBAAA;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCzB,eAAe,CAACwB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAChCZ,aAAa,CAAC,EAAAW,gBAAA,GAAAC,IAAI,CAACE,UAAU,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBZ,UAAU,KAAI,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,0BAA0B,CAAC;MACxD,IAAID,QAAQ,CAACG,EAAE,EAAE;QAAA,IAAAO,UAAA;QACf,MAAML,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCvB,YAAY,CAAC,EAAA2B,UAAA,GAAAL,IAAI,CAACA,IAAI,cAAAK,UAAA,uBAATA,UAAA,CAAWC,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,IAAI,CAAC,KAAI,EAAE,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMV,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,kEAAkE,CAAC;MAChG,IAAID,QAAQ,CAACG,EAAE,EAAE;QACf,MAAME,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCrB,qBAAqB,CAACoB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACxC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC;EAED,MAAMT,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,6CAA6CC,kBAAkB,CAACR,YAAY,CAAC,EAAE,CAAC;MAC7G,IAAIM,QAAQ,CAACG,EAAE,EAAE;QACf,MAAME,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCnB,aAAa,CAACkB,IAAI,CAACA,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;EAED,MAAMM,sBAAsB,GAAIC,EAAU,IAAK;IAC7CC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,iBAAiBH,EAAE,EAAE;EAC9C,CAAC;EAED,MAAMI,YAAY,GAAIC,SAAiB,IAAa;IAClD,MAAMC,KAAgC,GAAG;MACvC,SAAS,EAAE,IAAI;MACf,UAAU,EAAE,IAAI;MAChB,eAAe,EAAE,IAAI;MACrB,QAAQ,EAAE,IAAI;MACd,SAAS,EAAE,IAAI;MACf,UAAU,EAAE,IAAI;MAChB,UAAU,EAAE,IAAI;MAChB,WAAW,EAAE,IAAI;MACjB,KAAK,EAAE,IAAI;MACX,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,KAAK,CAACD,SAAS,CAACE,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;EAC/C,CAAC;EAED,MAAMC,aAAa,GAAIH,SAAiB,IAAa;IACnD,MAAMI,MAAiC,GAAG;MACxC,SAAS,EAAE,MAAM;MACjB,UAAU,EAAE,MAAM;MAClB,eAAe,EAAE,MAAM;MACvB,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE,QAAQ;MACnB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,QAAQ;MACpB,WAAW,EAAE,QAAQ;MACrB,KAAK,EAAE,QAAQ;MACf,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACJ,SAAS,CAACE,WAAW,CAAC,CAAC,CAAC,IAAI,MAAM;EAClD,CAAC;EAED,oBACEjD,OAAA;IAAKoD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCrD,OAAA;MAAKoD,SAAS,EAAE,yBAAyBF,aAAa,CAAC7B,YAAY,CAAC,WAAW6B,aAAa,CAAC7B,YAAY,CAAC,uBAAwB;MAAAgC,QAAA,eAChIrD,OAAA;QAAKoD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDrD,OAAA;UAAKoD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCrD,OAAA,CAACJ,IAAI;YACH0D,EAAE,EAAC,eAAe;YAClBF,SAAS,EAAE,0BAA0BF,aAAa,CAAC7B,YAAY,CAAC,sDAAuD;YAAAgC,QAAA,gBAEvHrD,OAAA;cAAKoD,SAAS,EAAC,cAAc;cAACG,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eACjFrD,OAAA;gBAAM0D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,sBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENjE,OAAA;UAAKoD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCrD,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BP,YAAY,CAACzB,YAAY;UAAC;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACNjE,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAIoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,aACvC,EAAChC,YAAY;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACLjE,OAAA;cAAGoD,SAAS,EAAE,gBAAgBF,aAAa,CAAC7B,YAAY,CAAC,MAAO;cAAAgC,QAAA,EAAC;YAEjE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpD,UAAU,iBACTb,OAAA;MAAKoD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCrD,OAAA;QAAKoD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDrD,OAAA;UAAKoD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDrD,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrD,OAAA;cAAKoD,SAAS,EAAE,2BAA2BF,aAAa,CAAC7B,YAAY,CAAC,WAAY;cAAAgC,QAAA,EAC/ExC,UAAU,CAACqD;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACNjE,OAAA;cAAKoD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAiB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAENjE,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrD,OAAA;cAAKoD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACpDxC,UAAU,CAACsD;YAAgB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACNjE,OAAA;cAAKoD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAgB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAENjE,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BrD,OAAA;cAAKoD,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAClDxC,UAAU,CAACuD;YAAkB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNjE,OAAA;cAAKoD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAe;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDjE,OAAA;MAAKoD,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DrD,OAAA;QAAKoD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9CrD,OAAA;UAAKoD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBrD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBrD,OAAA;cAAIoD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,GAAC,aACzC,EAAChC,YAAY,EAAC,IAAE,EAACd,YAAY,CAAC8D,MAAM,EAAC,GAClD;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAGLlD,OAAO,gBACNf,OAAA;YAAKoD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnD,CAAC,GAAGiB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAChC,GAAG,CAAC,CAACiC,CAAC,EAAEC,KAAK,kBAC1BxE,OAAA;cAAiBoD,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACtFrD,OAAA;gBAAKoD,SAAS,EAAC;cAA2B;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjDjE,OAAA;gBAAKoD,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBrD,OAAA;kBAAKoD,SAAS,EAAC;gBAAoC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DjE,OAAA;kBAAKoD,SAAS,EAAC;gBAAoC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DjE,OAAA;kBAAKoD,SAAS,EAAC;gBAAqC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3DjE,OAAA;kBAAKoD,SAAS,EAAC;gBAAsC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA,GAPEO,KAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,GACJ1D,YAAY,CAAC8D,MAAM,KAAK,CAAC,gBAC3BrE,OAAA;YAAKoD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrD,OAAA;cAAKoD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvCjE,OAAA;cAAIoD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjE,OAAA;cAAGoD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAENjE,OAAA,CAAAE,SAAA;YAAAmD,QAAA,gBACErD,OAAA;cAAKoD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD9C,YAAY,CAAC+B,GAAG,CAAC,CAACmC,WAAW,EAAED,KAAK,kBACnCxE,OAAA,CAACF,uBAAuB;gBAEtB4C,EAAE,EAAE+B,WAAW,CAAC/B,EAAG;gBACnBgC,KAAK,EAAED,WAAW,CAACC,KAAM;gBACzBC,SAAS,EAAEF,WAAW,CAACE,SAAU;gBACjCC,QAAQ,EAAEH,WAAW,CAACG,QAAS;gBAC/BC,MAAM,EAAEJ,WAAW,CAACI,MAAO;gBAC3BxE,KAAK,EAAEoE,WAAW,CAACpE,KAAM;gBACzByE,OAAO,EAAEL,WAAW,CAACK,OAAQ;gBAC7BC,OAAO,EAAEtC,sBAAuB;gBAChC+B,KAAK,EAAEA;cAAM,GATRC,WAAW,CAAC/B,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUpB,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGL9C,UAAU,GAAG,CAAC,iBACbnB,OAAA;cAAKoD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxCrD,OAAA;gBAAKoD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC5B,CAAC,GAAGiB,KAAK,CAACnD,UAAU,CAAC,CAAC,CAACmB,GAAG,CAAC,CAACiC,CAAC,EAAEC,KAAK,kBACnCxE,OAAA;kBAEE+E,OAAO,EAAEA,CAAA,KAAM7D,cAAc,CAACsD,KAAK,GAAG,CAAC,CAAE;kBACzCpB,SAAS,EAAE,mEACTnC,WAAW,KAAKuD,KAAK,GAAG,CAAC,GACrB,MAAMtB,aAAa,CAAC7B,YAAY,CAAC,iBAAiB,GAClD,0CAA0C,EAC7C;kBAAAgC,QAAA,EAEFmB,KAAK,GAAG;gBAAC,GARLA,KAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjE,OAAA;UAAKoD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAEvBrD,OAAA;YAAKoD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDrD,OAAA;cAAIoD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjE,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB5C,SAAS,CAACuE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK5D,YAAY,CAAC,CAACiB,GAAG,CAAES,SAAS,iBACvD/C,OAAA,CAACJ,IAAI;gBAEH0D,EAAE,EAAE,uBAAuBzB,kBAAkB,CAACkB,SAAS,CAAC,EAAG;gBAC3DK,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAE5FrD,OAAA;kBAAMoD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEP,YAAY,CAACC,SAAS;gBAAC;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChEjE,OAAA;kBAAMoD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEN;gBAAS;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GALzDlB,SAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMV,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjE,OAAA;YAAKoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDrD,OAAA;cAAIoD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjE,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB1C,kBAAkB,CAACuE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5C,GAAG,CAAEmC,WAAW,iBAC9CzE,OAAA;gBAEE+E,OAAO,EAAEA,CAAA,KAAMtC,sBAAsB,CAACgC,WAAW,CAAC/B,EAAE,CAAE;gBACtDU,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,gBAEzFrD,OAAA;kBAAIoD,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAChEoB,WAAW,CAACC;gBAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACLjE,OAAA;kBAAGoD,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACtCoB,WAAW,CAACK,OAAO,EAAC,UAAG,EAACL,WAAW,CAACpE,KAAK;gBAAA;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACJjE,OAAA;kBAAKoD,SAAS,EAAE,uEACdqB,WAAW,CAACI,MAAM,GACd,6BAA6B,GAC7B,yBAAyB,EAC5B;kBAAAxB,QAAA,EACAoB,WAAW,CAACI,MAAM,GAAG,QAAQ,GAAG;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA,GAhBDQ,WAAW,CAAC/B,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBhB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAnTID,mBAA6B;EAAA,QACfR,SAAS,EACFE,WAAW;AAAA;AAAAsF,EAAA,GAFhChF,mBAA6B;AAqTnC,eAAeA,mBAAmB;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}