{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/OpportunitiesByType.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OpportunitiesByType = () => {\n  _s();\n  const {\n    type\n  } = useParams();\n  const {\n    translations\n  } = useLanguage();\n  const [opportunities, setOpportunities] = useState([]);\n  const [allTypes, setAllTypes] = useState([]);\n  const [latestOpportunities, setLatestOpportunities] = useState([]);\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const decodedType = type ? decodeURIComponent(type) : '';\n  useEffect(() => {\n    if (decodedType) {\n      fetchOpportunitiesByType();\n      fetchAllTypes();\n      fetchLatestOpportunities();\n      fetchTypeStatistics();\n    }\n  }, [decodedType, currentPage]);\n  const fetchOpportunitiesByType = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/opportunities/type/${encodeURIComponent(decodedType)}?page=${currentPage}&limit=12`);\n      if (response.ok) {\n        var _data$pagination;\n        const data = await response.json();\n        setOpportunities(data.data || []);\n        setTotalPages(((_data$pagination = data.pagination) === null || _data$pagination === void 0 ? void 0 : _data$pagination.totalPages) || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching opportunities by type:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchAllTypes = async () => {\n    try {\n      const response = await fetch('/api/opportunities/types');\n      if (response.ok) {\n        var _data$data;\n        const data = await response.json();\n        setAllTypes(((_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.map(item => item.name)) || []);\n      }\n    } catch (error) {\n      console.error('Error fetching types:', error);\n    }\n  };\n  const fetchLatestOpportunities = async () => {\n    try {\n      const response = await fetch('/api/opportunities?limit=6&orderBy=created_at&orderDirection=DESC');\n      if (response.ok) {\n        const data = await response.json();\n        setLatestOpportunities(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching latest opportunities:', error);\n    }\n  };\n  const fetchTypeStatistics = async () => {\n    try {\n      const response = await fetch(`/api/opportunities/types/statistics?type=${encodeURIComponent(decodedType)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching type statistics:', error);\n    }\n  };\n  const getTypeIcon = typeName => {\n    const icons = {\n      'internship': '🎓',\n      'training': '📚',\n      'conference': '🎤',\n      'workshop': '🔧',\n      'competition': '🏆'\n    };\n    return icons[typeName.toLowerCase()] || '📋';\n  };\n  const getTypeColor = typeName => {\n    const colors = {\n      'internship': 'blue',\n      'training': 'green',\n      'conference': 'purple',\n      'workshop': 'orange',\n      'competition': 'red'\n    };\n    return colors[typeName.toLowerCase()] || 'gray';\n  };\n  const getTypeLabel = typeName => {\n    const labels = {\n      'internship': 'Stages',\n      'training': 'Formations',\n      'conference': 'Conférences',\n      'workshop': 'Ateliers',\n      'competition': 'Concours'\n    };\n    return labels[typeName.toLowerCase()] || typeName;\n  };\n  const isExpired = deadline => {\n    return new Date(deadline) < new Date();\n  };\n  const getDaysUntilDeadline = deadline => {\n    const today = new Date();\n    const deadlineDate = new Date(deadline);\n    const diffTime = deadlineDate.getTime() - today.getTime();\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-gradient-to-r from-${getTypeColor(decodedType)}-600 to-${getTypeColor(decodedType)}-700 text-white py-16`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-6\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/opportunities\",\n            className: `flex items-center text-${getTypeColor(decodedType)}-200 hover:text-white transition-colors duration-200`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-5 w-5 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 19l-7-7 7-7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), \"Retour aux opportunit\\xE9s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-6xl mr-4\",\n            children: getTypeIcon(decodedType)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl md:text-5xl font-bold mb-2\",\n              children: getTypeLabel(decodedType)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xl text-${getTypeColor(decodedType)}-100`,\n              children: \"D\\xE9couvrez toutes les opportunit\\xE9s de ce type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), statistics && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white py-12 border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `text-3xl font-bold text-${getTypeColor(decodedType)}-600 mb-2`,\n              children: statistics.totalOpportunities\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Total des opportunit\\xE9s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-green-600 mb-2\",\n              children: statistics.activeOpportunities\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Opportunit\\xE9s actives\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-red-600 mb-2\",\n              children: statistics.inactiveOpportunities\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-600\",\n              children: \"Opportunit\\xE9s ferm\\xE9es\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-2/3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: [getTypeLabel(decodedType), \" (\", opportunities.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [...Array(6)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"aspect-[16/9] bg-gray-200\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-5 bg-gray-200 rounded w-3/4 mb-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-1/2 mb-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-full mb-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-10 bg-gray-100 rounded w-full mt-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this) : opportunities.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-6xl mb-4\",\n              children: getTypeIcon(decodedType)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"Aucune opportunit\\xE9 trouv\\xE9e\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Aucune opportunit\\xE9 n'est disponible pour ce type actuellement.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: opportunities.map(opportunity => {\n                const expired = isExpired(opportunity.deadline);\n                const daysLeft = getDaysUntilDeadline(opportunity.deadline);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n                  children: [opportunity.thumbnail && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"aspect-[16/9] bg-gray-200\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: opportunity.thumbnail,\n                      alt: opportunity.title,\n                      className: \"w-full h-full object-cover\",\n                      onError: e => {\n                        const target = e.target;\n                        target.src = '/assets/default-opportunity.jpg';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getTypeColor(decodedType)}-100 text-${getTypeColor(decodedType)}-800`,\n                        children: [getTypeIcon(decodedType), \" \", getTypeLabel(decodedType)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 282,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${expired ? 'bg-red-100 text-red-800' : daysLeft <= 7 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`,\n                        children: expired ? 'Expiré' : daysLeft <= 0 ? 'Aujourd\\'hui' : `${daysLeft} jours`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 mb-2 line-clamp-2\",\n                      children: opportunity.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 text-sm mb-3 line-clamp-2\",\n                      children: opportunity.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2 mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center text-sm text-gray-600\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"h-4 w-4 mr-2\",\n                          fill: \"none\",\n                          stroke: \"currentColor\",\n                          viewBox: \"0 0 24 24\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 305,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 304,\n                          columnNumber: 31\n                        }, this), opportunity.organization]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 303,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center text-sm text-gray-600\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"h-4 w-4 mr-2\",\n                          fill: \"none\",\n                          stroke: \"currentColor\",\n                          viewBox: \"0 0 24 24\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 312,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 313,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 311,\n                          columnNumber: 31\n                        }, this), opportunity.isRemote ? 'À distance' : opportunity.location]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center text-sm text-gray-600\",\n                        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"h-4 w-4 mr-2\",\n                          fill: \"none\",\n                          stroke: \"currentColor\",\n                          viewBox: \"0 0 24 24\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 320,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 319,\n                          columnNumber: 31\n                        }, this), \"Date limite: \", formatDate(opportunity.deadline)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 318,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 27\n                    }, this), opportunity.applicationLink && /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: opportunity.applicationLink,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: `inline-flex items-center justify-center w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-${getTypeColor(decodedType)}-600 hover:bg-${getTypeColor(decodedType)}-700 transition-colors duration-200`,\n                      children: [\"Postuler maintenant\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"ml-2 h-4 w-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 2,\n                          d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 335,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 334,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 25\n                  }, this)]\n                }, opportunity.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center mt-12\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [...Array(totalPages)].map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setCurrentPage(index + 1),\n                  className: `px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${currentPage === index + 1 ? `bg-${getTypeColor(decodedType)}-600 text-white` : 'bg-white text-gray-700 hover:bg-gray-100'}`,\n                  children: index + 1\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-1/3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Autres Types d'Opportunit\\xE9s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: allTypes.filter(t => t !== decodedType).map(typeName => /*#__PURE__*/_jsxDEV(Link, {\n                to: `/opportunities/type/${encodeURIComponent(typeName)}`,\n                className: \"flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl mr-3\",\n                  children: getTypeIcon(typeName)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900\",\n                  children: getTypeLabel(typeName)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)]\n              }, typeName, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-md p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"Derni\\xE8res Opportunit\\xE9s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: latestOpportunities.slice(0, 5).map(opportunity => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900 text-sm mb-1 line-clamp-2\",\n                  children: opportunity.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-600 mb-2\",\n                  children: [opportunity.organization, \" \\u2022 \", getTypeLabel(opportunity.type)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${opportunity.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: opportunity.isActive ? 'Actif' : 'Fermé'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)]\n              }, opportunity.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s(OpportunitiesByType, \"vE0OBjfddMKCaPXWbwThC+f0m0A=\", false, function () {\n  return [useParams, useLanguage];\n});\n_c = OpportunitiesByType;\nexport default OpportunitiesByType;\nvar _c;\n$RefreshReg$(_c, \"OpportunitiesByType\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OpportunitiesByType", "_s", "type", "translations", "opportunities", "setOpportunities", "allTypes", "setAllTypes", "latestOpportunities", "setLatestOpportunities", "statistics", "setStatistics", "loading", "setLoading", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "decodedType", "decodeURIComponent", "fetchOpportunitiesByType", "fetchAllTypes", "fetchLatestOpportunities", "fetchTypeStatistics", "response", "fetch", "encodeURIComponent", "ok", "_data$pagination", "data", "json", "pagination", "error", "console", "_data$data", "map", "item", "name", "getTypeIcon", "typeName", "icons", "toLowerCase", "getTypeColor", "colors", "getTypeLabel", "labels", "isExpired", "deadline", "Date", "getDaysUntilDeadline", "today", "deadlineDate", "diffTime", "getTime", "Math", "ceil", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "className", "children", "to", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalOpportunities", "activeOpportunities", "inactiveOpportunities", "length", "Array", "_", "index", "opportunity", "expired", "daysLeft", "thumbnail", "src", "alt", "title", "onError", "e", "target", "description", "organization", "isRemote", "location", "applicationLink", "href", "rel", "id", "onClick", "filter", "t", "slice", "isActive", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/OpportunitiesByType.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\n\ninterface Opportunity {\n  id: number;\n  title: string;\n  description: string;\n  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';\n  organization: string;\n  location: string;\n  isRemote: boolean;\n  deadline: string;\n  startDate?: string;\n  endDate?: string;\n  applicationLink?: string;\n  thumbnail?: string;\n  isActive: boolean;\n  tags?: string[];\n}\n\ninterface TypeStatistics {\n  type: string;\n  totalOpportunities: number;\n  activeOpportunities: number;\n  inactiveOpportunities: number;\n}\n\nconst OpportunitiesByType: React.FC = () => {\n  const { type } = useParams<{ type: string }>();\n  const { translations } = useLanguage();\n  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);\n  const [allTypes, setAllTypes] = useState<string[]>([]);\n  const [latestOpportunities, setLatestOpportunities] = useState<Opportunity[]>([]);\n  const [statistics, setStatistics] = useState<TypeStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  const decodedType = type ? decodeURIComponent(type) : '';\n\n  useEffect(() => {\n    if (decodedType) {\n      fetchOpportunitiesByType();\n      fetchAllTypes();\n      fetchLatestOpportunities();\n      fetchTypeStatistics();\n    }\n  }, [decodedType, currentPage]);\n\n  const fetchOpportunitiesByType = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/opportunities/type/${encodeURIComponent(decodedType)}?page=${currentPage}&limit=12`);\n      if (response.ok) {\n        const data = await response.json();\n        setOpportunities(data.data || []);\n        setTotalPages(data.pagination?.totalPages || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching opportunities by type:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchAllTypes = async () => {\n    try {\n      const response = await fetch('/api/opportunities/types');\n      if (response.ok) {\n        const data = await response.json();\n        setAllTypes(data.data?.map((item: any) => item.name) || []);\n      }\n    } catch (error) {\n      console.error('Error fetching types:', error);\n    }\n  };\n\n  const fetchLatestOpportunities = async () => {\n    try {\n      const response = await fetch('/api/opportunities?limit=6&orderBy=created_at&orderDirection=DESC');\n      if (response.ok) {\n        const data = await response.json();\n        setLatestOpportunities(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching latest opportunities:', error);\n    }\n  };\n\n  const fetchTypeStatistics = async () => {\n    try {\n      const response = await fetch(`/api/opportunities/types/statistics?type=${encodeURIComponent(decodedType)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching type statistics:', error);\n    }\n  };\n\n  const getTypeIcon = (typeName: string): string => {\n    const icons: { [key: string]: string } = {\n      'internship': '🎓',\n      'training': '📚',\n      'conference': '🎤',\n      'workshop': '🔧',\n      'competition': '🏆'\n    };\n    return icons[typeName.toLowerCase()] || '📋';\n  };\n\n  const getTypeColor = (typeName: string): string => {\n    const colors: { [key: string]: string } = {\n      'internship': 'blue',\n      'training': 'green',\n      'conference': 'purple',\n      'workshop': 'orange',\n      'competition': 'red'\n    };\n    return colors[typeName.toLowerCase()] || 'gray';\n  };\n\n  const getTypeLabel = (typeName: string): string => {\n    const labels: { [key: string]: string } = {\n      'internship': 'Stages',\n      'training': 'Formations',\n      'conference': 'Conférences',\n      'workshop': 'Ateliers',\n      'competition': 'Concours'\n    };\n    return labels[typeName.toLowerCase()] || typeName;\n  };\n\n  const isExpired = (deadline: string): boolean => {\n    return new Date(deadline) < new Date();\n  };\n\n  const getDaysUntilDeadline = (deadline: string): number => {\n    const today = new Date();\n    const deadlineDate = new Date(deadline);\n    const diffTime = deadlineDate.getTime() - today.getTime();\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  };\n\n  const formatDate = (dateString: string): string => {\n    return new Date(dateString).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <div className={`bg-gradient-to-r from-${getTypeColor(decodedType)}-600 to-${getTypeColor(decodedType)}-700 text-white py-16`}>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center mb-6\">\n            <Link\n              to=\"/opportunities\"\n              className={`flex items-center text-${getTypeColor(decodedType)}-200 hover:text-white transition-colors duration-200`}\n            >\n              <svg className=\"h-5 w-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n              Retour aux opportunités\n            </Link>\n          </div>\n          \n          <div className=\"flex items-center mb-4\">\n            <div className=\"text-6xl mr-4\">\n              {getTypeIcon(decodedType)}\n            </div>\n            <div>\n              <h1 className=\"text-4xl md:text-5xl font-bold mb-2\">\n                {getTypeLabel(decodedType)}\n              </h1>\n              <p className={`text-xl text-${getTypeColor(decodedType)}-100`}>\n                Découvrez toutes les opportunités de ce type\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Section */}\n      {statistics && (\n        <div className=\"bg-white py-12 border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div className=\"text-center\">\n                <div className={`text-3xl font-bold text-${getTypeColor(decodedType)}-600 mb-2`}>\n                  {statistics.totalOpportunities}\n                </div>\n                <div className=\"text-gray-600\">Total des opportunités</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600 mb-2\">\n                  {statistics.activeOpportunities}\n                </div>\n                <div className=\"text-gray-600\">Opportunités actives</div>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-red-600 mb-2\">\n                  {statistics.inactiveOpportunities}\n                </div>\n                <div className=\"text-gray-600\">Opportunités fermées</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Main Content Area */}\n          <div className=\"lg:w-2/3\">\n            <div className=\"mb-8\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                {getTypeLabel(decodedType)} ({opportunities.length})\n              </h2>\n            </div>\n\n            {/* Opportunities Grid */}\n            {loading ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {[...Array(6)].map((_, index) => (\n                  <div key={index} className=\"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\">\n                    <div className=\"aspect-[16/9] bg-gray-200\"></div>\n                    <div className=\"p-6\">\n                      <div className=\"h-5 bg-gray-200 rounded w-3/4 mb-3\"></div>\n                      <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-3\"></div>\n                      <div className=\"h-4 bg-gray-200 rounded w-full mb-3\"></div>\n                      <div className=\"h-10 bg-gray-100 rounded w-full mt-4\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : opportunities.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <div className=\"text-6xl mb-4\">{getTypeIcon(decodedType)}</div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                  Aucune opportunité trouvée\n                </h3>\n                <p className=\"text-gray-600\">\n                  Aucune opportunité n'est disponible pour ce type actuellement.\n                </p>\n              </div>\n            ) : (\n              <>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {opportunities.map((opportunity) => {\n                    const expired = isExpired(opportunity.deadline);\n                    const daysLeft = getDaysUntilDeadline(opportunity.deadline);\n                    \n                    return (\n                      <div\n                        key={opportunity.id}\n                        className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\"\n                      >\n                        {opportunity.thumbnail && (\n                          <div className=\"aspect-[16/9] bg-gray-200\">\n                            <img\n                              src={opportunity.thumbnail}\n                              alt={opportunity.title}\n                              className=\"w-full h-full object-cover\"\n                              onError={(e) => {\n                                const target = e.target as HTMLImageElement;\n                                target.src = '/assets/default-opportunity.jpg';\n                              }}\n                            />\n                          </div>\n                        )}\n                        \n                        <div className=\"p-6\">\n                          <div className=\"flex items-center justify-between mb-3\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getTypeColor(decodedType)}-100 text-${getTypeColor(decodedType)}-800`}>\n                              {getTypeIcon(decodedType)} {getTypeLabel(decodedType)}\n                            </span>\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              expired ? 'bg-red-100 text-red-800' : \n                              daysLeft <= 7 ? 'bg-yellow-100 text-yellow-800' : \n                              'bg-green-100 text-green-800'\n                            }`}>\n                              {expired ? 'Expiré' : daysLeft <= 0 ? 'Aujourd\\'hui' : `${daysLeft} jours`}\n                            </span>\n                          </div>\n                          \n                          <h3 className=\"text-lg font-semibold text-gray-900 mb-2 line-clamp-2\">\n                            {opportunity.title}\n                          </h3>\n                          \n                          <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n                            {opportunity.description}\n                          </p>\n                          \n                          <div className=\"space-y-2 mb-4\">\n                            <div className=\"flex items-center text-sm text-gray-600\">\n                              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                              </svg>\n                              {opportunity.organization}\n                            </div>\n                            \n                            <div className=\"flex items-center text-sm text-gray-600\">\n                              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                              </svg>\n                              {opportunity.isRemote ? 'À distance' : opportunity.location}\n                            </div>\n                            \n                            <div className=\"flex items-center text-sm text-gray-600\">\n                              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                              </svg>\n                              Date limite: {formatDate(opportunity.deadline)}\n                            </div>\n                          </div>\n                          \n                          {opportunity.applicationLink && (\n                            <a\n                              href={opportunity.applicationLink}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className={`inline-flex items-center justify-center w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-${getTypeColor(decodedType)}-600 hover:bg-${getTypeColor(decodedType)}-700 transition-colors duration-200`}\n                            >\n                              Postuler maintenant\n                              <svg className=\"ml-2 h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                              </svg>\n                            </a>\n                          )}\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n\n                {/* Pagination */}\n                {totalPages > 1 && (\n                  <div className=\"flex justify-center mt-12\">\n                    <div className=\"flex space-x-2\">\n                      {[...Array(totalPages)].map((_, index) => (\n                        <button\n                          key={index}\n                          onClick={() => setCurrentPage(index + 1)}\n                          className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${\n                            currentPage === index + 1\n                              ? `bg-${getTypeColor(decodedType)}-600 text-white`\n                              : 'bg-white text-gray-700 hover:bg-gray-100'\n                          }`}\n                        >\n                          {index + 1}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"lg:w-1/3\">\n            {/* Other Types */}\n            <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Autres Types d'Opportunités\n              </h3>\n              <div className=\"space-y-2\">\n                {allTypes.filter(t => t !== decodedType).map((typeName) => (\n                  <Link\n                    key={typeName}\n                    to={`/opportunities/type/${encodeURIComponent(typeName)}`}\n                    className=\"flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <span className=\"text-2xl mr-3\">{getTypeIcon(typeName)}</span>\n                    <span className=\"font-medium text-gray-900\">{getTypeLabel(typeName)}</span>\n                  </Link>\n                ))}\n              </div>\n            </div>\n\n            {/* Latest Opportunities */}\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Dernières Opportunités\n              </h3>\n              <div className=\"space-y-4\">\n                {latestOpportunities.slice(0, 5).map((opportunity) => (\n                  <div\n                    key={opportunity.id}\n                    className=\"cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <h4 className=\"font-medium text-gray-900 text-sm mb-1 line-clamp-2\">\n                      {opportunity.title}\n                    </h4>\n                    <p className=\"text-xs text-gray-600 mb-2\">\n                      {opportunity.organization} • {getTypeLabel(opportunity.type)}\n                    </p>\n                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      opportunity.isActive \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {opportunity.isActive ? 'Actif' : 'Fermé'}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default OpportunitiesByType;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA0BzD,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM;IAAEC;EAAK,CAAC,GAAGT,SAAS,CAAmB,CAAC;EAC9C,MAAM;IAAEU;EAAa,CAAC,GAAGR,WAAW,CAAC,CAAC;EACtC,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAgB,EAAE,CAAC;EACrE,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAW,EAAE,CAAC;EACtD,MAAM,CAACiB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlB,QAAQ,CAAgB,EAAE,CAAC;EACjF,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAwB,IAAI,CAAC;EACzE,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAM2B,WAAW,GAAGhB,IAAI,GAAGiB,kBAAkB,CAACjB,IAAI,CAAC,GAAG,EAAE;EAExDV,SAAS,CAAC,MAAM;IACd,IAAI0B,WAAW,EAAE;MACfE,wBAAwB,CAAC,CAAC;MAC1BC,aAAa,CAAC,CAAC;MACfC,wBAAwB,CAAC,CAAC;MAC1BC,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACL,WAAW,EAAEJ,WAAW,CAAC,CAAC;EAE9B,MAAMM,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,2BAA2BC,kBAAkB,CAACR,WAAW,CAAC,SAASJ,WAAW,WAAW,CAAC;MACvH,IAAIU,QAAQ,CAACG,EAAE,EAAE;QAAA,IAAAC,gBAAA;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCzB,gBAAgB,CAACwB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QACjCZ,aAAa,CAAC,EAAAW,gBAAA,GAAAC,IAAI,CAACE,UAAU,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBZ,UAAU,KAAI,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,0BAA0B,CAAC;MACxD,IAAID,QAAQ,CAACG,EAAE,EAAE;QAAA,IAAAO,UAAA;QACf,MAAML,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCvB,WAAW,CAAC,EAAA2B,UAAA,GAAAL,IAAI,CAACA,IAAI,cAAAK,UAAA,uBAATA,UAAA,CAAWC,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,IAAI,CAAC,KAAI,EAAE,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMV,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,mEAAmE,CAAC;MACjG,IAAID,QAAQ,CAACG,EAAE,EAAE;QACf,MAAME,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCrB,sBAAsB,CAACoB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACzC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D;EACF,CAAC;EAED,MAAMT,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4CAA4CC,kBAAkB,CAACR,WAAW,CAAC,EAAE,CAAC;MAC3G,IAAIM,QAAQ,CAACG,EAAE,EAAE;QACf,MAAME,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCnB,aAAa,CAACkB,IAAI,CAACA,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMM,WAAW,GAAIC,QAAgB,IAAa;IAChD,MAAMC,KAAgC,GAAG;MACvC,YAAY,EAAE,IAAI;MAClB,UAAU,EAAE,IAAI;MAChB,YAAY,EAAE,IAAI;MAClB,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE;IACjB,CAAC;IACD,OAAOA,KAAK,CAACD,QAAQ,CAACE,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;EAC9C,CAAC;EAED,MAAMC,YAAY,GAAIH,QAAgB,IAAa;IACjD,MAAMI,MAAiC,GAAG;MACxC,YAAY,EAAE,MAAM;MACpB,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,QAAQ;MACtB,UAAU,EAAE,QAAQ;MACpB,aAAa,EAAE;IACjB,CAAC;IACD,OAAOA,MAAM,CAACJ,QAAQ,CAACE,WAAW,CAAC,CAAC,CAAC,IAAI,MAAM;EACjD,CAAC;EAED,MAAMG,YAAY,GAAIL,QAAgB,IAAa;IACjD,MAAMM,MAAiC,GAAG;MACxC,YAAY,EAAE,QAAQ;MACtB,UAAU,EAAE,YAAY;MACxB,YAAY,EAAE,aAAa;MAC3B,UAAU,EAAE,UAAU;MACtB,aAAa,EAAE;IACjB,CAAC;IACD,OAAOA,MAAM,CAACN,QAAQ,CAACE,WAAW,CAAC,CAAC,CAAC,IAAIF,QAAQ;EACnD,CAAC;EAED,MAAMO,SAAS,GAAIC,QAAgB,IAAc;IAC/C,OAAO,IAAIC,IAAI,CAACD,QAAQ,CAAC,GAAG,IAAIC,IAAI,CAAC,CAAC;EACxC,CAAC;EAED,MAAMC,oBAAoB,GAAIF,QAAgB,IAAa;IACzD,MAAMG,KAAK,GAAG,IAAIF,IAAI,CAAC,CAAC;IACxB,MAAMG,YAAY,GAAG,IAAIH,IAAI,CAACD,QAAQ,CAAC;IACvC,MAAMK,QAAQ,GAAGD,YAAY,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC;IACzD,OAAOC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACpD,CAAC;EAED,MAAMI,UAAU,GAAIC,UAAkB,IAAa;IACjD,OAAO,IAAIT,IAAI,CAACS,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,oBACEhE,OAAA;IAAKiE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtClE,OAAA;MAAKiE,SAAS,EAAE,yBAAyBpB,YAAY,CAACxB,WAAW,CAAC,WAAWwB,YAAY,CAACxB,WAAW,CAAC,uBAAwB;MAAA6C,QAAA,eAC5HlE,OAAA;QAAKiE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlE,OAAA;UAAKiE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrClE,OAAA,CAACH,IAAI;YACHsE,EAAE,EAAC,gBAAgB;YACnBF,SAAS,EAAE,0BAA0BpB,YAAY,CAACxB,WAAW,CAAC,sDAAuD;YAAA6C,QAAA,gBAErHlE,OAAA;cAAKiE,SAAS,EAAC,cAAc;cAACG,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eACjFlE,OAAA;gBAAMuE,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,8BAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEN9E,OAAA;UAAKiE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrClE,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3BzB,WAAW,CAACpB,WAAW;UAAC;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACN9E,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAIiE,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAChDnB,YAAY,CAAC1B,WAAW;YAAC;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACL9E,OAAA;cAAGiE,SAAS,EAAE,gBAAgBpB,YAAY,CAACxB,WAAW,CAAC,MAAO;cAAA6C,QAAA,EAAC;YAE/D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjE,UAAU,iBACTb,OAAA;MAAKiE,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtClE,OAAA;QAAKiE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDlE,OAAA;UAAKiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDlE,OAAA;YAAKiE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlE,OAAA;cAAKiE,SAAS,EAAE,2BAA2BpB,YAAY,CAACxB,WAAW,CAAC,WAAY;cAAA6C,QAAA,EAC7ErD,UAAU,CAACkE;YAAkB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACN9E,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAEN9E,OAAA;YAAKiE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlE,OAAA;cAAKiE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACpDrD,UAAU,CAACmE;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACN9E,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEN9E,OAAA;YAAKiE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlE,OAAA;cAAKiE,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAClDrD,UAAU,CAACoE;YAAqB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACN9E,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9E,OAAA;MAAKiE,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DlE,OAAA;QAAKiE,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9ClE,OAAA;UAAKiE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBlE,OAAA;YAAKiE,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBlE,OAAA;cAAIiE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,GAClDnB,YAAY,CAAC1B,WAAW,CAAC,EAAC,IAAE,EAACd,aAAa,CAAC2E,MAAM,EAAC,GACrD;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAGL/D,OAAO,gBACNf,OAAA;YAAKiE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnD,CAAC,GAAGiB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC7C,GAAG,CAAC,CAAC8C,CAAC,EAAEC,KAAK,kBAC1BrF,OAAA;cAAiBiE,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACtFlE,OAAA;gBAAKiE,SAAS,EAAC;cAA2B;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjD9E,OAAA;gBAAKiE,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBlE,OAAA;kBAAKiE,SAAS,EAAC;gBAAoC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1D9E,OAAA;kBAAKiE,SAAS,EAAC;gBAAoC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1D9E,OAAA;kBAAKiE,SAAS,EAAC;gBAAqC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3D9E,OAAA;kBAAKiE,SAAS,EAAC;gBAAsC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA,GAPEO,KAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,GACJvE,aAAa,CAAC2E,MAAM,KAAK,CAAC,gBAC5BlF,OAAA;YAAKiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClE,OAAA;cAAKiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEzB,WAAW,CAACpB,WAAW;YAAC;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/D9E,OAAA;cAAIiE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9E,OAAA;cAAGiE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAEN9E,OAAA,CAAAE,SAAA;YAAAgE,QAAA,gBACElE,OAAA;cAAKiE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD3D,aAAa,CAAC+B,GAAG,CAAEgD,WAAW,IAAK;gBAClC,MAAMC,OAAO,GAAGtC,SAAS,CAACqC,WAAW,CAACpC,QAAQ,CAAC;gBAC/C,MAAMsC,QAAQ,GAAGpC,oBAAoB,CAACkC,WAAW,CAACpC,QAAQ,CAAC;gBAE3D,oBACElD,OAAA;kBAEEiE,SAAS,EAAC,8FAA8F;kBAAAC,QAAA,GAEvGoB,WAAW,CAACG,SAAS,iBACpBzF,OAAA;oBAAKiE,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxClE,OAAA;sBACE0F,GAAG,EAAEJ,WAAW,CAACG,SAAU;sBAC3BE,GAAG,EAAEL,WAAW,CAACM,KAAM;sBACvB3B,SAAS,EAAC,4BAA4B;sBACtC4B,OAAO,EAAGC,CAAC,IAAK;wBACd,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA0B;wBAC3CA,MAAM,CAACL,GAAG,GAAG,iCAAiC;sBAChD;oBAAE;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN,eAED9E,OAAA;oBAAKiE,SAAS,EAAC,KAAK;oBAAAC,QAAA,gBAClBlE,OAAA;sBAAKiE,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDlE,OAAA;wBAAMiE,SAAS,EAAE,8EAA8EpB,YAAY,CAACxB,WAAW,CAAC,aAAawB,YAAY,CAACxB,WAAW,CAAC,MAAO;wBAAA6C,QAAA,GAClKzB,WAAW,CAACpB,WAAW,CAAC,EAAC,GAAC,EAAC0B,YAAY,CAAC1B,WAAW,CAAC;sBAAA;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjD,CAAC,eACP9E,OAAA;wBAAMiE,SAAS,EAAE,2EACfsB,OAAO,GAAG,yBAAyB,GACnCC,QAAQ,IAAI,CAAC,GAAG,+BAA+B,GAC/C,6BAA6B,EAC5B;wBAAAtB,QAAA,EACAqB,OAAO,GAAG,QAAQ,GAAGC,QAAQ,IAAI,CAAC,GAAG,cAAc,GAAG,GAAGA,QAAQ;sBAAQ;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eAEN9E,OAAA;sBAAIiE,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EAClEoB,WAAW,CAACM;oBAAK;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eAEL9E,OAAA;sBAAGiE,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EACnDoB,WAAW,CAACU;oBAAW;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,eAEJ9E,OAAA;sBAAKiE,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BlE,OAAA;wBAAKiE,SAAS,EAAC,yCAAyC;wBAAAC,QAAA,gBACtDlE,OAAA;0BAAKiE,SAAS,EAAC,cAAc;0BAACG,IAAI,EAAC,MAAM;0BAACC,MAAM,EAAC,cAAc;0BAACC,OAAO,EAAC,WAAW;0BAAAJ,QAAA,eACjFlE,OAAA;4BAAMuE,aAAa,EAAC,OAAO;4BAACC,cAAc,EAAC,OAAO;4BAACC,WAAW,EAAE,CAAE;4BAACC,CAAC,EAAC;0BAA2I;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChN,CAAC,EACLQ,WAAW,CAACW,YAAY;sBAAA;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,eAEN9E,OAAA;wBAAKiE,SAAS,EAAC,yCAAyC;wBAAAC,QAAA,gBACtDlE,OAAA;0BAAKiE,SAAS,EAAC,cAAc;0BAACG,IAAI,EAAC,MAAM;0BAACC,MAAM,EAAC,cAAc;0BAACC,OAAO,EAAC,WAAW;0BAAAJ,QAAA,gBACjFlE,OAAA;4BAAMuE,aAAa,EAAC,OAAO;4BAACC,cAAc,EAAC,OAAO;4BAACC,WAAW,EAAE,CAAE;4BAACC,CAAC,EAAC;0BAAoF;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC5J9E,OAAA;4BAAMuE,aAAa,EAAC,OAAO;4BAACC,cAAc,EAAC,OAAO;4BAACC,WAAW,EAAE,CAAE;4BAACC,CAAC,EAAC;0BAAkC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvG,CAAC,EACLQ,WAAW,CAACY,QAAQ,GAAG,YAAY,GAAGZ,WAAW,CAACa,QAAQ;sBAAA;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD,CAAC,eAEN9E,OAAA;wBAAKiE,SAAS,EAAC,yCAAyC;wBAAAC,QAAA,gBACtDlE,OAAA;0BAAKiE,SAAS,EAAC,cAAc;0BAACG,IAAI,EAAC,MAAM;0BAACC,MAAM,EAAC,cAAc;0BAACC,OAAO,EAAC,WAAW;0BAAAJ,QAAA,eACjFlE,OAAA;4BAAMuE,aAAa,EAAC,OAAO;4BAACC,cAAc,EAAC,OAAO;4BAACC,WAAW,EAAE,CAAE;4BAACC,CAAC,EAAC;0BAAwF;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7J,CAAC,iBACO,EAACnB,UAAU,CAAC2B,WAAW,CAACpC,QAAQ,CAAC;sBAAA;wBAAAyB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAELQ,WAAW,CAACc,eAAe,iBAC1BpG,OAAA;sBACEqG,IAAI,EAAEf,WAAW,CAACc,eAAgB;sBAClCL,MAAM,EAAC,QAAQ;sBACfO,GAAG,EAAC,qBAAqB;sBACzBrC,SAAS,EAAE,mIAAmIpB,YAAY,CAACxB,WAAW,CAAC,iBAAiBwB,YAAY,CAACxB,WAAW,CAAC,qCAAsC;sBAAA6C,QAAA,GACxP,qBAEC,eAAAlE,OAAA;wBAAKiE,SAAS,EAAC,cAAc;wBAACG,IAAI,EAAC,MAAM;wBAACC,MAAM,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAJ,QAAA,eACjFlE,OAAA;0BAAMuE,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAACC,CAAC,EAAC;wBAA8E;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GA5EDQ,WAAW,CAACiB,EAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6EhB,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGL3D,UAAU,GAAG,CAAC,iBACbnB,OAAA;cAAKiE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxClE,OAAA;gBAAKiE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC5B,CAAC,GAAGiB,KAAK,CAAChE,UAAU,CAAC,CAAC,CAACmB,GAAG,CAAC,CAAC8C,CAAC,EAAEC,KAAK,kBACnCrF,OAAA;kBAEEwG,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAACmE,KAAK,GAAG,CAAC,CAAE;kBACzCpB,SAAS,EAAE,mEACThD,WAAW,KAAKoE,KAAK,GAAG,CAAC,GACrB,MAAMxC,YAAY,CAACxB,WAAW,CAAC,iBAAiB,GAChD,0CAA0C,EAC7C;kBAAA6C,QAAA,EAEFmB,KAAK,GAAG;gBAAC,GARLA,KAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9E,OAAA;UAAKiE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAEvBlE,OAAA;YAAKiE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlE,OAAA;cAAIiE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9E,OAAA;cAAKiE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBzD,QAAQ,CAACgG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKrF,WAAW,CAAC,CAACiB,GAAG,CAAEI,QAAQ,iBACpD1C,OAAA,CAACH,IAAI;gBAEHsE,EAAE,EAAE,uBAAuBtC,kBAAkB,CAACa,QAAQ,CAAC,EAAG;gBAC1DuB,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAE5FlE,OAAA;kBAAMiE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEzB,WAAW,CAACC,QAAQ;gBAAC;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9D9E,OAAA;kBAAMiE,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEnB,YAAY,CAACL,QAAQ;gBAAC;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GALtEpC,QAAQ;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMT,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9E,OAAA;YAAKiE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDlE,OAAA;cAAIiE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9E,OAAA;cAAKiE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBvD,mBAAmB,CAACgG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrE,GAAG,CAAEgD,WAAW,iBAC/CtF,OAAA;gBAEEiE,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,gBAEzFlE,OAAA;kBAAIiE,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAChEoB,WAAW,CAACM;gBAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACL9E,OAAA;kBAAGiE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACtCoB,WAAW,CAACW,YAAY,EAAC,UAAG,EAAClD,YAAY,CAACuC,WAAW,CAACjF,IAAI,CAAC;gBAAA;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACJ9E,OAAA;kBAAKiE,SAAS,EAAE,uEACdqB,WAAW,CAACsB,QAAQ,GAChB,6BAA6B,GAC7B,yBAAyB,EAC5B;kBAAA1C,QAAA,EACAoB,WAAW,CAACsB,QAAQ,GAAG,OAAO,GAAG;gBAAO;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA,GAfDQ,WAAW,CAACiB,EAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBhB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1E,EAAA,CA1YID,mBAA6B;EAAA,QAChBP,SAAS,EACDE,WAAW;AAAA;AAAA+G,EAAA,GAFhC1G,mBAA6B;AA4YnC,eAAeA,mBAAmB;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}