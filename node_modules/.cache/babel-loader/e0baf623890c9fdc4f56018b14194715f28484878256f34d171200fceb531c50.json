{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16.85 18.58a9 9 0 1 0-9.7 0\",\n  key: \"d71mpg\"\n}], [\"path\", {\n  d: \"M8 14a5 5 0 1 1 8 0\",\n  key: \"fc81rn\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"11\",\n  r: \"1\",\n  key: \"1gvufo\"\n}], [\"path\", {\n  d: \"M13 17a1 1 0 1 0-2 0l.5 4.5a.5.5 0 1 0 1 0Z\",\n  key: \"za5kbj\"\n}]];\nconst Podcast = createLucideIcon(\"podcast\", __iconNode);\nexport { __iconNode, Podcast as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Podcast", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/podcast.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16.85 18.58a9 9 0 1 0-9.7 0', key: 'd71mpg' }],\n  ['path', { d: 'M8 14a5 5 0 1 1 8 0', key: 'fc81rn' }],\n  ['circle', { cx: '12', cy: '11', r: '1', key: '1gvufo' }],\n  ['path', { d: 'M13 17a1 1 0 1 0-2 0l.5 4.5a.5.5 0 1 0 1 0Z', key: 'za5kbj' }],\n];\n\n/**\n * @component @name Podcast\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYuODUgMTguNThhOSA5IDAgMSAwLTkuNyAwIiAvPgogIDxwYXRoIGQ9Ik04IDE0YTUgNSAwIDEgMSA4IDAiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxMSIgcj0iMSIgLz4KICA8cGF0aCBkPSJNMTMgMTdhMSAxIDAgMSAwLTIgMGwuNSA0LjVhLjUuNSAwIDEgMCAxIDBaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/podcast\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Podcast = createLucideIcon('podcast', __iconNode);\n\nexport default Podcast;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAU,GAC9E;AAaM,MAAAI,OAAA,GAAUC,gBAAiB,YAAWP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}