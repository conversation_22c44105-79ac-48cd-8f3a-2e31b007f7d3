{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 17H7l-4 4v-7\",\n  key: \"1r71xu\"\n}], [\"path\", {\n  d: \"M14 17h1\",\n  key: \"nufu4t\"\n}], [\"path\", {\n  d: \"M14 3h1\",\n  key: \"1ec4yj\"\n}], [\"path\", {\n  d: \"M19 3a2 2 0 0 1 2 2\",\n  key: \"18rm91\"\n}], [\"path\", {\n  d: \"M21 14v1a2 2 0 0 1-2 2\",\n  key: \"29akq3\"\n}], [\"path\", {\n  d: \"M21 9v1\",\n  key: \"mxsmne\"\n}], [\"path\", {\n  d: \"M3 9v1\",\n  key: \"1r0deq\"\n}], [\"path\", {\n  d: \"M5 3a2 2 0 0 0-2 2\",\n  key: \"y57alp\"\n}], [\"path\", {\n  d: \"M9 3h1\",\n  key: \"1yesri\"\n}]];\nconst MessageSquareDashed = createLucideIcon(\"message-square-dashed\", __iconNode);\nexport { __iconNode, MessageSquareDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageSquareDashed", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/message-square-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 17H7l-4 4v-7', key: '1r71xu' }],\n  ['path', { d: 'M14 17h1', key: 'nufu4t' }],\n  ['path', { d: 'M14 3h1', key: '1ec4yj' }],\n  ['path', { d: 'M19 3a2 2 0 0 1 2 2', key: '18rm91' }],\n  ['path', { d: 'M21 14v1a2 2 0 0 1-2 2', key: '29akq3' }],\n  ['path', { d: 'M21 9v1', key: 'mxsmne' }],\n  ['path', { d: 'M3 9v1', key: '1r0deq' }],\n  ['path', { d: 'M5 3a2 2 0 0 0-2 2', key: 'y57alp' }],\n  ['path', { d: 'M9 3h1', key: '1yesri' }],\n];\n\n/**\n * @component @name MessageSquareDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTdIN2wtNCA0di03IiAvPgogIDxwYXRoIGQ9Ik0xNCAxN2gxIiAvPgogIDxwYXRoIGQ9Ik0xNCAzaDEiIC8+CiAgPHBhdGggZD0iTTE5IDNhMiAyIDAgMCAxIDIgMiIgLz4KICA8cGF0aCBkPSJNMjEgMTR2MWEyIDIgMCAwIDEtMiAyIiAvPgogIDxwYXRoIGQ9Ik0yMSA5djEiIC8+CiAgPHBhdGggZD0iTTMgOXYxIiAvPgogIDxwYXRoIGQ9Ik01IDNhMiAyIDAgMCAwLTIgMiIgLz4KICA8cGF0aCBkPSJNOSAzaDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-square-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquareDashed = createLucideIcon('message-square-dashed', __iconNode);\n\nexport default MessageSquareDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAU,GACzC;AAaM,MAAAC,mBAAA,GAAsBC,gBAAiB,0BAAyBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}