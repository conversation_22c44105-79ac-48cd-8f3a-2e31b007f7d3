{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalPageLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport ProfessionalSidebar from './ProfessionalSidebar';\nimport dataPrefetcher from '../utils/dataPrefetcher';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfessionalPageLayout = ({\n  hero,\n  statistics,\n  filters = [],\n  onFilterChange,\n  sidebarConfig,\n  children,\n  loading = false,\n  className = ''\n}) => {\n  _s();\n  const [currentFilters, setCurrentFilters] = React.useState({});\n\n  // Prefetch related data when component mounts\n  useEffect(() => {\n    // Prefetch common routes on initial load\n    dataPrefetcher.prefetchCommonRoutes();\n\n    // Prefetch related data based on current page\n    if (sidebarConfig.currentItem) {\n      dataPrefetcher.prefetchRelatedData(sidebarConfig.type, sidebarConfig.currentItem);\n    }\n  }, [sidebarConfig]);\n  const handleFilterChange = (key, value) => {\n    const newFilters = {\n      ...currentFilters,\n      [key]: value\n    };\n    setCurrentFilters(newFilters);\n    onFilterChange === null || onFilterChange === void 0 ? void 0 : onFilterChange(newFilters);\n  };\n  const getHeroBackgroundClass = () => {\n    if (hero.backgroundColor) {\n      return hero.backgroundColor;\n    }\n\n    // Default colors based on page type\n    switch (sidebarConfig.type) {\n      case 'countries':\n        return 'bg-gradient-to-r from-blue-600 to-blue-800';\n      case 'levels':\n        return 'bg-gradient-to-r from-green-600 to-green-800';\n      case 'opportunities':\n        return 'bg-gradient-to-r from-purple-600 to-purple-800';\n      default:\n        return 'bg-gradient-to-r from-gray-600 to-gray-800';\n    }\n  };\n  const renderHeroSection = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${getHeroBackgroundClass()} ${hero.textColor || 'text-white'} relative overflow-hidden`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        style: {\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [hero.icon && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-7xl md:text-8xl mb-6 animate-bounce-slow\",\n          children: hero.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-6xl font-bold mb-6 leading-tight\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-200\",\n            children: hero.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), hero.subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl opacity-90 max-w-4xl mx-auto leading-relaxed\",\n          children: hero.subtitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-0 left-0 right-0\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        viewBox: \"0 0 1200 120\",\n        preserveAspectRatio: \"none\",\n        className: \"relative block w-full h-12 fill-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n  const renderStatistics = () => {\n    if (!statistics) return null;\n    const statsData = [{\n      value: statistics.total,\n      label: statistics.label,\n      color: 'blue',\n      icon: '📊',\n      gradient: 'from-blue-500 to-blue-600'\n    }, {\n      value: statistics.active,\n      label: statistics.activeLabel,\n      color: 'green',\n      icon: '✅',\n      gradient: 'from-green-500 to-green-600'\n    }, ...(statistics.inactive !== undefined && statistics.inactiveLabel ? [{\n      value: statistics.inactive,\n      label: statistics.inactiveLabel,\n      color: 'red',\n      icon: '❌',\n      gradient: 'from-red-500 to-red-600'\n    }] : [])];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: statsData.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg border border-gray-100 p-8 text-center transform hover:scale-105 transition-transform duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-12 h-12 bg-gradient-to-r ${stat.gradient} rounded-full flex items-center justify-center shadow-lg`,\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-xl\",\n                    children: stat.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-4xl font-bold bg-gradient-to-r ${stat.gradient} bg-clip-text text-transparent mb-2`,\n                  children: stat.value.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-600 font-medium\",\n                  children: stat.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-200 rounded-full h-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `bg-gradient-to-r ${stat.gradient} h-2 rounded-full transition-all duration-1000 ease-out`,\n                    style: {\n                      width: `${Math.min(stat.value / Math.max(...statsData.map(s => s.value)) * 100, 100)}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFilters = () => {\n    if (filters.length === 0) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-4\",\n          children: filters.map(filter => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"min-w-0 flex-1 md:flex-none md:w-48\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: filter.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: currentFilters[filter.key] || '',\n              onChange: e => handleFilterChange(filter.key, e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Tous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), filter.options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: option.value,\n                children: option.label\n              }, option.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, filter.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n  };\n  const renderMainContent = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col lg:flex-row gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:w-2/3\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProfessionalSidebar, {\n        config: sidebarConfig\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      global: true,\n      children: `\n        @keyframes bounce-slow {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0);\n          }\n          40% {\n            transform: translateY(-10px);\n          }\n          60% {\n            transform: translateY(-5px);\n          }\n        }\n        .animate-bounce-slow {\n          animation: bounce-slow 3s infinite;\n        }\n\n        @keyframes fade-in-up {\n          from {\n            opacity: 0;\n            transform: translateY(30px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n        .animate-fade-in-up {\n          animation: fade-in-up 0.6s ease-out;\n        }\n\n        @keyframes slide-in-right {\n          from {\n            opacity: 0;\n            transform: translateX(30px);\n          }\n          to {\n            opacity: 1;\n            transform: translateX(0);\n          }\n        }\n        .animate-slide-in-right {\n          animation: slide-in-right 0.8s ease-out;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `min-h-screen bg-gray-50 ${className}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-fade-in-up\",\n        children: renderHeroSection()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-fade-in-up\",\n        style: {\n          animationDelay: '0.2s'\n        },\n        children: renderStatistics()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-fade-in-up\",\n        style: {\n          animationDelay: '0.4s'\n        },\n        children: renderFilters()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-fade-in-up\",\n        style: {\n          animationDelay: '0.6s'\n        },\n        children: renderMainContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n/**\n * Professional Content Grid Component\n * Unified grid for displaying scholarships or opportunities\n */\n_s(ProfessionalPageLayout, \"I5viA6RSVVBvxvuXyBCoLNiZdWM=\");\n_c = ProfessionalPageLayout;\nexport const ProfessionalContentGrid = ({\n  items,\n  loading,\n  emptyMessage,\n  emptyIcon,\n  renderItem,\n  pagination\n}) => {\n  const renderLoadingGrid = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n    children: [...Array(6)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"aspect-[16/9] bg-gray-200\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-5 bg-gray-200 rounded w-3/4 mb-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-200 rounded w-1/2 mb-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-4 bg-gray-200 rounded w-full mb-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-10 bg-gray-100 rounded w-full mt-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 337,\n    columnNumber: 5\n  }, this);\n  const renderEmptyState = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center py-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-6xl mb-4\",\n      children: emptyIcon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-xl font-semibold text-gray-900 mb-2\",\n      children: \"Aucun r\\xE9sultat trouv\\xE9\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600\",\n      children: emptyMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 353,\n    columnNumber: 5\n  }, this);\n  const renderPagination = () => {\n    if (!pagination || pagination.totalPages <= 1) return null;\n    const {\n      currentPage,\n      totalPages,\n      onPageChange\n    } = pagination;\n    const pages = [];\n\n    // Add page numbers\n    for (let i = 1; i <= totalPages; i++) {\n      if (i === 1 || i === totalPages || i >= currentPage - 2 && i <= currentPage + 2) {\n        pages.push(i);\n      } else if (i === currentPage - 3 || i === currentPage + 3) {\n        pages.push('...');\n      }\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center space-x-2 mt-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onPageChange(currentPage - 1),\n        disabled: currentPage === 1,\n        className: \"px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n        children: \"Pr\\xE9c\\xE9dent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), pages.map((page, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => typeof page === 'number' ? onPageChange(page) : undefined,\n        disabled: typeof page !== 'number',\n        className: `px-3 py-2 rounded-md border ${page === currentPage ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'} ${typeof page !== 'number' ? 'cursor-default' : 'cursor-pointer'}`,\n        children: page\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onPageChange(currentPage + 1),\n        disabled: currentPage === totalPages,\n        className: \"px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n        children: \"Suivant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return renderLoadingGrid();\n  }\n  if (items.length === 0) {\n    return renderEmptyState();\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: items.map(renderItem)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 7\n    }, this), renderPagination()]\n  }, void 0, true);\n};\n_c2 = ProfessionalContentGrid;\nexport default ProfessionalPageLayout;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProfessionalPageLayout\");\n$RefreshReg$(_c2, \"ProfessionalContentGrid\");", "map": {"version": 3, "names": ["React", "useEffect", "ProfessionalSidebar", "dataPrefetcher", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfessionalPageLayout", "hero", "statistics", "filters", "onFilterChange", "sidebarConfig", "children", "loading", "className", "_s", "currentFilters", "setCurrentFilters", "useState", "prefetchCommonRoutes", "currentItem", "prefetchRelatedData", "type", "handleFilterChange", "key", "value", "newFilters", "getHeroBackgroundClass", "backgroundColor", "renderHeroSection", "textColor", "style", "backgroundImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "title", "subtitle", "viewBox", "preserveAspectRatio", "d", "renderStatistics", "statsData", "total", "label", "color", "gradient", "active", "activeLabel", "inactive", "undefined", "inactiveLabel", "map", "stat", "index", "toLocaleString", "width", "Math", "min", "max", "s", "renderFilters", "length", "filter", "onChange", "e", "target", "options", "option", "<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "config", "jsx", "global", "animationDelay", "_c", "ProfessionalContentGrid", "items", "emptyMessage", "emptyIcon", "renderItem", "pagination", "renderLoadingGrid", "Array", "_", "renderEmptyState", "renderPagination", "totalPages", "currentPage", "onPageChange", "pages", "i", "push", "onClick", "disabled", "page", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalPageLayout.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport ProfessionalSidebar from './ProfessionalSidebar';\nimport { SidebarConfig } from '../services/sidebarService';\nimport dataPrefetcher from '../utils/dataPrefetcher';\n\ninterface HeroSection {\n  title: string;\n  subtitle?: string;\n  icon?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\ninterface StatisticsData {\n  total: number;\n  active: number;\n  inactive?: number;\n  label: string;\n  activeLabel: string;\n  inactiveLabel?: string;\n}\n\ninterface FilterOption {\n  key: string;\n  label: string;\n  value: string;\n  options: Array<{ value: string; label: string }>;\n}\n\ninterface ProfessionalPageLayoutProps {\n  hero: HeroSection;\n  statistics?: StatisticsData;\n  filters?: FilterOption[];\n  onFilterChange?: (filters: Record<string, string>) => void;\n  sidebarConfig: SidebarConfig;\n  children: React.ReactNode;\n  loading?: boolean;\n  className?: string;\n}\n\nconst ProfessionalPageLayout: React.FC<ProfessionalPageLayoutProps> = ({\n  hero,\n  statistics,\n  filters = [],\n  onFilterChange,\n  sidebarConfig,\n  children,\n  loading = false,\n  className = ''\n}) => {\n  const [currentFilters, setCurrentFilters] = React.useState<Record<string, string>>({});\n\n  // Prefetch related data when component mounts\n  useEffect(() => {\n    // Prefetch common routes on initial load\n    dataPrefetcher.prefetchCommonRoutes();\n\n    // Prefetch related data based on current page\n    if (sidebarConfig.currentItem) {\n      dataPrefetcher.prefetchRelatedData(sidebarConfig.type, sidebarConfig.currentItem);\n    }\n  }, [sidebarConfig]);\n\n  const handleFilterChange = (key: string, value: string) => {\n    const newFilters = { ...currentFilters, [key]: value };\n    setCurrentFilters(newFilters);\n    onFilterChange?.(newFilters);\n  };\n\n  const getHeroBackgroundClass = () => {\n    if (hero.backgroundColor) {\n      return hero.backgroundColor;\n    }\n    \n    // Default colors based on page type\n    switch (sidebarConfig.type) {\n      case 'countries':\n        return 'bg-gradient-to-r from-blue-600 to-blue-800';\n      case 'levels':\n        return 'bg-gradient-to-r from-green-600 to-green-800';\n      case 'opportunities':\n        return 'bg-gradient-to-r from-purple-600 to-purple-800';\n      default:\n        return 'bg-gradient-to-r from-gray-600 to-gray-800';\n    }\n  };\n\n  const renderHeroSection = () => (\n    <div className={`${getHeroBackgroundClass()} ${hero.textColor || 'text-white'} relative overflow-hidden`}>\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n        }}></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"text-center\">\n          {hero.icon && (\n            <div className=\"text-7xl md:text-8xl mb-6 animate-bounce-slow\">\n              {hero.icon}\n            </div>\n          )}\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6 leading-tight\">\n            <span className=\"bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-200\">\n              {hero.title}\n            </span>\n          </h1>\n          {hero.subtitle && (\n            <p className=\"text-xl md:text-2xl opacity-90 max-w-4xl mx-auto leading-relaxed\">\n              {hero.subtitle}\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Bottom Wave */}\n      <div className=\"absolute bottom-0 left-0 right-0\">\n        <svg viewBox=\"0 0 1200 120\" preserveAspectRatio=\"none\" className=\"relative block w-full h-12 fill-gray-50\">\n          <path d=\"M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z\"></path>\n        </svg>\n      </div>\n    </div>\n  );\n\n  const renderStatistics = () => {\n    if (!statistics) return null;\n\n    const statsData = [\n      {\n        value: statistics.total,\n        label: statistics.label,\n        color: 'blue',\n        icon: '📊',\n        gradient: 'from-blue-500 to-blue-600'\n      },\n      {\n        value: statistics.active,\n        label: statistics.activeLabel,\n        color: 'green',\n        icon: '✅',\n        gradient: 'from-green-500 to-green-600'\n      },\n      ...(statistics.inactive !== undefined && statistics.inactiveLabel ? [{\n        value: statistics.inactive,\n        label: statistics.inactiveLabel,\n        color: 'red',\n        icon: '❌',\n        gradient: 'from-red-500 to-red-600'\n      }] : [])\n    ];\n\n    return (\n      <div className=\"bg-gray-50 border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {statsData.map((stat, index) => (\n              <div key={index} className=\"relative\">\n                <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 p-8 text-center transform hover:scale-105 transition-transform duration-300\">\n                  <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\n                    <div className={`w-12 h-12 bg-gradient-to-r ${stat.gradient} rounded-full flex items-center justify-center shadow-lg`}>\n                      <span className=\"text-white text-xl\">{stat.icon}</span>\n                    </div>\n                  </div>\n                  <div className=\"mt-6\">\n                    <div className={`text-4xl font-bold bg-gradient-to-r ${stat.gradient} bg-clip-text text-transparent mb-2`}>\n                      {stat.value.toLocaleString()}\n                    </div>\n                    <div className=\"text-gray-600 font-medium\">{stat.label}</div>\n                  </div>\n\n                  {/* Progress bar for visual appeal */}\n                  <div className=\"mt-4\">\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className={`bg-gradient-to-r ${stat.gradient} h-2 rounded-full transition-all duration-1000 ease-out`}\n                        style={{\n                          width: `${Math.min((stat.value / Math.max(...statsData.map(s => s.value))) * 100, 100)}%`\n                        }}\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderFilters = () => {\n    if (filters.length === 0) return null;\n\n    return (\n      <div className=\"bg-gray-50 border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-wrap gap-4\">\n            {filters.map((filter) => (\n              <div key={filter.key} className=\"min-w-0 flex-1 md:flex-none md:w-48\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  {filter.label}\n                </label>\n                <select\n                  value={currentFilters[filter.key] || ''}\n                  onChange={(e) => handleFilterChange(filter.key, e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">Tous</option>\n                  {filter.options.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderMainContent = () => (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n      <div className=\"flex flex-col lg:flex-row gap-8\">\n        {/* Main Content */}\n        <div className=\"lg:w-2/3\">\n          {children}\n        </div>\n\n        {/* Sidebar */}\n        <ProfessionalSidebar config={sidebarConfig} />\n      </div>\n    </div>\n  );\n\n  return (\n    <>\n      {/* Custom CSS for animations */}\n      <style jsx global>{`\n        @keyframes bounce-slow {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0);\n          }\n          40% {\n            transform: translateY(-10px);\n          }\n          60% {\n            transform: translateY(-5px);\n          }\n        }\n        .animate-bounce-slow {\n          animation: bounce-slow 3s infinite;\n        }\n\n        @keyframes fade-in-up {\n          from {\n            opacity: 0;\n            transform: translateY(30px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n        .animate-fade-in-up {\n          animation: fade-in-up 0.6s ease-out;\n        }\n\n        @keyframes slide-in-right {\n          from {\n            opacity: 0;\n            transform: translateX(30px);\n          }\n          to {\n            opacity: 1;\n            transform: translateX(0);\n          }\n        }\n        .animate-slide-in-right {\n          animation: slide-in-right 0.8s ease-out;\n        }\n      `}</style>\n\n      <div className={`min-h-screen bg-gray-50 ${className}`}>\n        {/* Hero Section */}\n        <div className=\"animate-fade-in-up\">\n          {renderHeroSection()}\n        </div>\n\n        {/* Statistics Section */}\n        <div className=\"animate-fade-in-up\" style={{ animationDelay: '0.2s' }}>\n          {renderStatistics()}\n        </div>\n\n        {/* Filters Section */}\n        <div className=\"animate-fade-in-up\" style={{ animationDelay: '0.4s' }}>\n          {renderFilters()}\n        </div>\n\n        {/* Main Content with Sidebar */}\n        <div className=\"animate-fade-in-up\" style={{ animationDelay: '0.6s' }}>\n          {renderMainContent()}\n        </div>\n      </div>\n    </>\n  );\n};\n\n/**\n * Professional Content Grid Component\n * Unified grid for displaying scholarships or opportunities\n */\ninterface ContentGridProps {\n  items: any[];\n  loading: boolean;\n  emptyMessage: string;\n  emptyIcon: string;\n  renderItem: (item: any) => React.ReactNode;\n  pagination?: {\n    currentPage: number;\n    totalPages: number;\n    onPageChange: (page: number) => void;\n  };\n}\n\nexport const ProfessionalContentGrid: React.FC<ContentGridProps> = ({\n  items,\n  loading,\n  emptyMessage,\n  emptyIcon,\n  renderItem,\n  pagination\n}) => {\n  const renderLoadingGrid = () => (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      {[...Array(6)].map((_, index) => (\n        <div key={index} className=\"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\">\n          <div className=\"aspect-[16/9] bg-gray-200\"></div>\n          <div className=\"p-6\">\n            <div className=\"h-5 bg-gray-200 rounded w-3/4 mb-3\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-3\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-full mb-3\"></div>\n            <div className=\"h-10 bg-gray-100 rounded w-full mt-4\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n\n  const renderEmptyState = () => (\n    <div className=\"text-center py-12\">\n      <div className=\"text-6xl mb-4\">{emptyIcon}</div>\n      <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n        Aucun résultat trouvé\n      </h3>\n      <p className=\"text-gray-600\">\n        {emptyMessage}\n      </p>\n    </div>\n  );\n\n  const renderPagination = () => {\n    if (!pagination || pagination.totalPages <= 1) return null;\n\n    const { currentPage, totalPages, onPageChange } = pagination;\n    const pages = [];\n\n    // Add page numbers\n    for (let i = 1; i <= totalPages; i++) {\n      if (\n        i === 1 ||\n        i === totalPages ||\n        (i >= currentPage - 2 && i <= currentPage + 2)\n      ) {\n        pages.push(i);\n      } else if (\n        i === currentPage - 3 ||\n        i === currentPage + 3\n      ) {\n        pages.push('...');\n      }\n    }\n\n    return (\n      <div className=\"flex justify-center items-center space-x-2 mt-8\">\n        <button\n          onClick={() => onPageChange(currentPage - 1)}\n          disabled={currentPage === 1}\n          className=\"px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          Précédent\n        </button>\n\n        {pages.map((page, index) => (\n          <button\n            key={index}\n            onClick={() => typeof page === 'number' ? onPageChange(page) : undefined}\n            disabled={typeof page !== 'number'}\n            className={`px-3 py-2 rounded-md border ${\n              page === currentPage\n                ? 'border-blue-500 bg-blue-500 text-white'\n                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n            } ${typeof page !== 'number' ? 'cursor-default' : 'cursor-pointer'}`}\n          >\n            {page}\n          </button>\n        ))}\n\n        <button\n          onClick={() => onPageChange(currentPage + 1)}\n          disabled={currentPage === totalPages}\n          className=\"px-3 py-2 rounded-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          Suivant\n        </button>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return renderLoadingGrid();\n  }\n\n  if (items.length === 0) {\n    return renderEmptyState();\n  }\n\n  return (\n    <>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {items.map(renderItem)}\n      </div>\n      {renderPagination()}\n    </>\n  );\n};\n\nexport default ProfessionalPageLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,mBAAmB,MAAM,uBAAuB;AAEvD,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqCrD,MAAMC,sBAA6D,GAAGA,CAAC;EACrEC,IAAI;EACJC,UAAU;EACVC,OAAO,GAAG,EAAE;EACZC,cAAc;EACdC,aAAa;EACbC,QAAQ;EACRC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,KAAK,CAACoB,QAAQ,CAAyB,CAAC,CAAC,CAAC;;EAEtF;EACAnB,SAAS,CAAC,MAAM;IACd;IACAE,cAAc,CAACkB,oBAAoB,CAAC,CAAC;;IAErC;IACA,IAAIR,aAAa,CAACS,WAAW,EAAE;MAC7BnB,cAAc,CAACoB,mBAAmB,CAACV,aAAa,CAACW,IAAI,EAAEX,aAAa,CAACS,WAAW,CAAC;IACnF;EACF,CAAC,EAAE,CAACT,aAAa,CAAC,CAAC;EAEnB,MAAMY,kBAAkB,GAAGA,CAACC,GAAW,EAAEC,KAAa,KAAK;IACzD,MAAMC,UAAU,GAAG;MAAE,GAAGV,cAAc;MAAE,CAACQ,GAAG,GAAGC;IAAM,CAAC;IACtDR,iBAAiB,CAACS,UAAU,CAAC;IAC7BhB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGgB,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIpB,IAAI,CAACqB,eAAe,EAAE;MACxB,OAAOrB,IAAI,CAACqB,eAAe;IAC7B;;IAEA;IACA,QAAQjB,aAAa,CAACW,IAAI;MACxB,KAAK,WAAW;QACd,OAAO,4CAA4C;MACrD,KAAK,QAAQ;QACX,OAAO,8CAA8C;MACvD,KAAK,eAAe;QAClB,OAAO,gDAAgD;MACzD;QACE,OAAO,4CAA4C;IACvD;EACF,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAAA,kBACxB1B,OAAA;IAAKW,SAAS,EAAE,GAAGa,sBAAsB,CAAC,CAAC,IAAIpB,IAAI,CAACuB,SAAS,IAAI,YAAY,2BAA4B;IAAAlB,QAAA,gBAEvGT,OAAA;MAAKW,SAAS,EAAC,6BAA6B;MAAAF,QAAA,eAC1CT,OAAA;QAAKW,SAAS,EAAC,kBAAkB;QAACiB,KAAK,EAAE;UACvCC,eAAe,EAAE;QACnB;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENjC,OAAA;MAAKW,SAAS,EAAC,uDAAuD;MAAAF,QAAA,eACpET,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAF,QAAA,GACzBL,IAAI,CAAC8B,IAAI,iBACRlC,OAAA;UAAKW,SAAS,EAAC,+CAA+C;UAAAF,QAAA,EAC3DL,IAAI,CAAC8B;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN,eACDjC,OAAA;UAAIW,SAAS,EAAC,mDAAmD;UAAAF,QAAA,eAC/DT,OAAA;YAAMW,SAAS,EAAC,uEAAuE;YAAAF,QAAA,EACpFL,IAAI,CAAC+B;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACJ7B,IAAI,CAACgC,QAAQ,iBACZpC,OAAA;UAAGW,SAAS,EAAC,kEAAkE;UAAAF,QAAA,EAC5EL,IAAI,CAACgC;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAKW,SAAS,EAAC,kCAAkC;MAAAF,QAAA,eAC/CT,OAAA;QAAKqC,OAAO,EAAC,cAAc;QAACC,mBAAmB,EAAC,MAAM;QAAC3B,SAAS,EAAC,yCAAyC;QAAAF,QAAA,eACxGT,OAAA;UAAMuC,CAAC,EAAC;QAAkM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/M;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACnC,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMoC,SAAS,GAAG,CAChB;MACEnB,KAAK,EAAEjB,UAAU,CAACqC,KAAK;MACvBC,KAAK,EAAEtC,UAAU,CAACsC,KAAK;MACvBC,KAAK,EAAE,MAAM;MACbV,IAAI,EAAE,IAAI;MACVW,QAAQ,EAAE;IACZ,CAAC,EACD;MACEvB,KAAK,EAAEjB,UAAU,CAACyC,MAAM;MACxBH,KAAK,EAAEtC,UAAU,CAAC0C,WAAW;MAC7BH,KAAK,EAAE,OAAO;MACdV,IAAI,EAAE,GAAG;MACTW,QAAQ,EAAE;IACZ,CAAC,EACD,IAAIxC,UAAU,CAAC2C,QAAQ,KAAKC,SAAS,IAAI5C,UAAU,CAAC6C,aAAa,GAAG,CAAC;MACnE5B,KAAK,EAAEjB,UAAU,CAAC2C,QAAQ;MAC1BL,KAAK,EAAEtC,UAAU,CAAC6C,aAAa;MAC/BN,KAAK,EAAE,KAAK;MACZV,IAAI,EAAE,GAAG;MACTW,QAAQ,EAAE;IACZ,CAAC,CAAC,GAAG,EAAE,CAAC,CACT;IAED,oBACE7C,OAAA;MAAKW,SAAS,EAAC,qBAAqB;MAAAF,QAAA,eAClCT,OAAA;QAAKW,SAAS,EAAC,8CAA8C;QAAAF,QAAA,eAC3DT,OAAA;UAAKW,SAAS,EAAC,uCAAuC;UAAAF,QAAA,EACnDgC,SAAS,CAACU,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBrD,OAAA;YAAiBW,SAAS,EAAC,UAAU;YAAAF,QAAA,eACnCT,OAAA;cAAKW,SAAS,EAAC,mIAAmI;cAAAF,QAAA,gBAChJT,OAAA;gBAAKW,SAAS,EAAC,qEAAqE;gBAAAF,QAAA,eAClFT,OAAA;kBAAKW,SAAS,EAAE,8BAA8ByC,IAAI,CAACP,QAAQ,0DAA2D;kBAAApC,QAAA,eACpHT,OAAA;oBAAMW,SAAS,EAAC,oBAAoB;oBAAAF,QAAA,EAAE2C,IAAI,CAAClB;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNjC,OAAA;gBAAKW,SAAS,EAAC,MAAM;gBAAAF,QAAA,gBACnBT,OAAA;kBAAKW,SAAS,EAAE,uCAAuCyC,IAAI,CAACP,QAAQ,qCAAsC;kBAAApC,QAAA,EACvG2C,IAAI,CAAC9B,KAAK,CAACgC,cAAc,CAAC;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNjC,OAAA;kBAAKW,SAAS,EAAC,2BAA2B;kBAAAF,QAAA,EAAE2C,IAAI,CAACT;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGNjC,OAAA;gBAAKW,SAAS,EAAC,MAAM;gBAAAF,QAAA,eACnBT,OAAA;kBAAKW,SAAS,EAAC,qCAAqC;kBAAAF,QAAA,eAClDT,OAAA;oBACEW,SAAS,EAAE,oBAAoByC,IAAI,CAACP,QAAQ,yDAA0D;oBACtGjB,KAAK,EAAE;sBACL2B,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAEL,IAAI,CAAC9B,KAAK,GAAGkC,IAAI,CAACE,GAAG,CAAC,GAAGjB,SAAS,CAACU,GAAG,CAACQ,CAAC,IAAIA,CAAC,CAACrC,KAAK,CAAC,CAAC,GAAI,GAAG,EAAE,GAAG,CAAC;oBACxF;kBAAE;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAzBEoB,KAAK;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAM2B,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAItD,OAAO,CAACuD,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAErC,oBACE7D,OAAA;MAAKW,SAAS,EAAC,qBAAqB;MAAAF,QAAA,eAClCT,OAAA;QAAKW,SAAS,EAAC,6CAA6C;QAAAF,QAAA,eAC1DT,OAAA;UAAKW,SAAS,EAAC,sBAAsB;UAAAF,QAAA,EAClCH,OAAO,CAAC6C,GAAG,CAAEW,MAAM,iBAClB9D,OAAA;YAAsBW,SAAS,EAAC,qCAAqC;YAAAF,QAAA,gBACnET,OAAA;cAAOW,SAAS,EAAC,8CAA8C;cAAAF,QAAA,EAC5DqD,MAAM,CAACnB;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACRjC,OAAA;cACEsB,KAAK,EAAET,cAAc,CAACiD,MAAM,CAACzC,GAAG,CAAC,IAAI,EAAG;cACxC0C,QAAQ,EAAGC,CAAC,IAAK5C,kBAAkB,CAAC0C,MAAM,CAACzC,GAAG,EAAE2C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;cAChEX,SAAS,EAAC,wIAAwI;cAAAF,QAAA,gBAElJT,OAAA;gBAAQsB,KAAK,EAAC,EAAE;gBAAAb,QAAA,EAAC;cAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC7B6B,MAAM,CAACI,OAAO,CAACf,GAAG,CAAEgB,MAAM,iBACzBnE,OAAA;gBAA2BsB,KAAK,EAAE6C,MAAM,CAAC7C,KAAM;gBAAAb,QAAA,EAC5C0D,MAAM,CAACxB;cAAK,GADFwB,MAAM,CAAC7C,KAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA,GAfD6B,MAAM,CAACzC,GAAG;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMmC,iBAAiB,GAAGA,CAAA,kBACxBpE,OAAA;IAAKW,SAAS,EAAC,8CAA8C;IAAAF,QAAA,eAC3DT,OAAA;MAAKW,SAAS,EAAC,iCAAiC;MAAAF,QAAA,gBAE9CT,OAAA;QAAKW,SAAS,EAAC,UAAU;QAAAF,QAAA,EACtBA;MAAQ;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNjC,OAAA,CAACH,mBAAmB;QAACwE,MAAM,EAAE7D;MAAc;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEjC,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBAEET,OAAA;MAAOsE,GAAG;MAACC,MAAM;MAAA9D,QAAA,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEVjC,OAAA;MAAKW,SAAS,EAAE,2BAA2BA,SAAS,EAAG;MAAAF,QAAA,gBAErDT,OAAA;QAAKW,SAAS,EAAC,oBAAoB;QAAAF,QAAA,EAChCiB,iBAAiB,CAAC;MAAC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGNjC,OAAA;QAAKW,SAAS,EAAC,oBAAoB;QAACiB,KAAK,EAAE;UAAE4C,cAAc,EAAE;QAAO,CAAE;QAAA/D,QAAA,EACnE+B,gBAAgB,CAAC;MAAC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAGNjC,OAAA;QAAKW,SAAS,EAAC,oBAAoB;QAACiB,KAAK,EAAE;UAAE4C,cAAc,EAAE;QAAO,CAAE;QAAA/D,QAAA,EACnEmD,aAAa,CAAC;MAAC;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAGNjC,OAAA;QAAKW,SAAS,EAAC,oBAAoB;QAACiB,KAAK,EAAE;UAAE4C,cAAc,EAAE;QAAO,CAAE;QAAA/D,QAAA,EACnE2D,iBAAiB,CAAC;MAAC;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AACA;AACA;AACA;AAHArB,EAAA,CA9QMT,sBAA6D;AAAAsE,EAAA,GAA7DtE,sBAA6D;AA+RnE,OAAO,MAAMuE,uBAAmD,GAAGA,CAAC;EAClEC,KAAK;EACLjE,OAAO;EACPkE,YAAY;EACZC,SAAS;EACTC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,MAAMC,iBAAiB,GAAGA,CAAA,kBACxBhF,OAAA;IAAKW,SAAS,EAAC,uCAAuC;IAAAF,QAAA,EACnD,CAAC,GAAGwE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC9B,GAAG,CAAC,CAAC+B,CAAC,EAAE7B,KAAK,kBAC1BrD,OAAA;MAAiBW,SAAS,EAAC,6DAA6D;MAAAF,QAAA,gBACtFT,OAAA;QAAKW,SAAS,EAAC;MAA2B;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjDjC,OAAA;QAAKW,SAAS,EAAC,KAAK;QAAAF,QAAA,gBAClBT,OAAA;UAAKW,SAAS,EAAC;QAAoC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DjC,OAAA;UAAKW,SAAS,EAAC;QAAoC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DjC,OAAA;UAAKW,SAAS,EAAC;QAAqC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3DjC,OAAA;UAAKW,SAAS,EAAC;QAAsC;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA,GAPEoB,KAAK;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQV,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMkD,gBAAgB,GAAGA,CAAA,kBACvBnF,OAAA;IAAKW,SAAS,EAAC,mBAAmB;IAAAF,QAAA,gBAChCT,OAAA;MAAKW,SAAS,EAAC,eAAe;MAAAF,QAAA,EAAEoE;IAAS;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAChDjC,OAAA;MAAIW,SAAS,EAAC,0CAA0C;MAAAF,QAAA,EAAC;IAEzD;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACLjC,OAAA;MAAGW,SAAS,EAAC,eAAe;MAAAF,QAAA,EACzBmE;IAAY;MAAA9C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACN;EAED,MAAMmD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACL,UAAU,IAAIA,UAAU,CAACM,UAAU,IAAI,CAAC,EAAE,OAAO,IAAI;IAE1D,MAAM;MAAEC,WAAW;MAAED,UAAU;MAAEE;IAAa,CAAC,GAAGR,UAAU;IAC5D,MAAMS,KAAK,GAAG,EAAE;;IAEhB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,UAAU,EAAEI,CAAC,EAAE,EAAE;MACpC,IACEA,CAAC,KAAK,CAAC,IACPA,CAAC,KAAKJ,UAAU,IACfI,CAAC,IAAIH,WAAW,GAAG,CAAC,IAAIG,CAAC,IAAIH,WAAW,GAAG,CAAE,EAC9C;QACAE,KAAK,CAACE,IAAI,CAACD,CAAC,CAAC;MACf,CAAC,MAAM,IACLA,CAAC,KAAKH,WAAW,GAAG,CAAC,IACrBG,CAAC,KAAKH,WAAW,GAAG,CAAC,EACrB;QACAE,KAAK,CAACE,IAAI,CAAC,KAAK,CAAC;MACnB;IACF;IAEA,oBACE1F,OAAA;MAAKW,SAAS,EAAC,iDAAiD;MAAAF,QAAA,gBAC9DT,OAAA;QACE2F,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACD,WAAW,GAAG,CAAC,CAAE;QAC7CM,QAAQ,EAAEN,WAAW,KAAK,CAAE;QAC5B3E,SAAS,EAAC,qIAAqI;QAAAF,QAAA,EAChJ;MAED;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERuD,KAAK,CAACrC,GAAG,CAAC,CAAC0C,IAAI,EAAExC,KAAK,kBACrBrD,OAAA;QAEE2F,OAAO,EAAEA,CAAA,KAAM,OAAOE,IAAI,KAAK,QAAQ,GAAGN,YAAY,CAACM,IAAI,CAAC,GAAG5C,SAAU;QACzE2C,QAAQ,EAAE,OAAOC,IAAI,KAAK,QAAS;QACnClF,SAAS,EAAE,+BACTkF,IAAI,KAAKP,WAAW,GAChB,wCAAwC,GACxC,yDAAyD,IAC3D,OAAOO,IAAI,KAAK,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB,EAAG;QAAApF,QAAA,EAEpEoF;MAAI,GATAxC,KAAK;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUJ,CACT,CAAC,eAEFjC,OAAA;QACE2F,OAAO,EAAEA,CAAA,KAAMJ,YAAY,CAACD,WAAW,GAAG,CAAC,CAAE;QAC7CM,QAAQ,EAAEN,WAAW,KAAKD,UAAW;QACrC1E,SAAS,EAAC,qIAAqI;QAAAF,QAAA,EAChJ;MAED;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,OAAOsE,iBAAiB,CAAC,CAAC;EAC5B;EAEA,IAAIL,KAAK,CAACd,MAAM,KAAK,CAAC,EAAE;IACtB,OAAOsB,gBAAgB,CAAC,CAAC;EAC3B;EAEA,oBACEnF,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACET,OAAA;MAAKW,SAAS,EAAC,uCAAuC;MAAAF,QAAA,EACnDkE,KAAK,CAACxB,GAAG,CAAC2B,UAAU;IAAC;MAAAhD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,EACLmD,gBAAgB,CAAC,CAAC;EAAA,eACnB,CAAC;AAEP,CAAC;AAACU,GAAA,GA9GWpB,uBAAmD;AAgHhE,eAAevE,sBAAsB;AAAC,IAAAsE,EAAA,EAAAqB,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}