{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 8 4-4 4 4\",\n  key: \"11wl7u\"\n}], [\"path\", {\n  d: \"M7 4v16\",\n  key: \"1glfcx\"\n}], [\"rect\", {\n  x: \"15\",\n  y: \"4\",\n  width: \"4\",\n  height: \"6\",\n  ry: \"2\",\n  key: \"1bwicg\"\n}], [\"path\", {\n  d: \"M17 20v-6h-2\",\n  key: \"1qp1so\"\n}], [\"path\", {\n  d: \"M15 20h4\",\n  key: \"1j968p\"\n}]];\nconst ArrowUp01 = createLucideIcon(\"arrow-up-0-1\", __iconNode);\nexport { __iconNode, ArrowUp01 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "ry", "ArrowUp01", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/arrow-up-0-1.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm3 8 4-4 4 4', key: '11wl7u' }],\n  ['path', { d: 'M7 4v16', key: '1glfcx' }],\n  ['rect', { x: '15', y: '4', width: '4', height: '6', ry: '2', key: '1bwicg' }],\n  ['path', { d: 'M17 20v-6h-2', key: '1qp1so' }],\n  ['path', { d: 'M15 20h4', key: '1j968p' }],\n];\n\n/**\n * @component @name ArrowUp01\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyA4IDQtNCA0IDQiIC8+CiAgPHBhdGggZD0iTTcgNHYxNiIgLz4KICA8cmVjdCB4PSIxNSIgeT0iNCIgd2lkdGg9IjQiIGhlaWdodD0iNiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTE3IDIwdi02aC0yIiAvPgogIDxwYXRoIGQ9Ik0xNSAyMGg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-up-0-1\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowUp01 = createLucideIcon('arrow-up-0-1', __iconNode);\n\nexport default ArrowUp01;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAM,SAAA,GAAYC,gBAAiB,iBAAgBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}