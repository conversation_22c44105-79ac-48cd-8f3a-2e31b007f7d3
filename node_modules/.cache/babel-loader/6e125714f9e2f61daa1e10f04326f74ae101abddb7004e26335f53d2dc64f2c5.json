{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { GraduationCap } from './icons/index';\nimport EnhancedScholarshipCard from './EnhancedScholarshipCard';\nimport scholarshipService from '../services/scholarshipService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedStudyLevelSection = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('licence');\n  const tabsRef = useRef(null);\n  const [loading, setLoading] = useState(true);\n  const [scholarships, setScholarships] = useState({\n    licence: [],\n    master: [],\n    doctorat: []\n  });\n\n  // Fetch scholarships by level\n  useEffect(() => {\n    const fetchScholarshipsByLevel = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch all scholarships\n        const allScholarships = await scholarshipService.getAllScholarships();\n\n        // Filter scholarships by level more precisely\n        const licenceScholarships = allScholarships.filter(scholarship => {\n          var _scholarship$level;\n          const level = ((_scholarship$level = scholarship.level) === null || _scholarship$level === void 0 ? void 0 : _scholarship$level.toLowerCase()) || '';\n          return level === 'licence' || level === 'undergraduate' || level === 'bachelor' || level.includes('licence') && !level.includes('master') && !level.includes('doctorat') || level.includes('undergraduate') && !level.includes('graduate') && !level.includes('phd');\n        });\n        const masterScholarships = allScholarships.filter(scholarship => {\n          var _scholarship$level2;\n          const level = ((_scholarship$level2 = scholarship.level) === null || _scholarship$level2 === void 0 ? void 0 : _scholarship$level2.toLowerCase()) || '';\n          return level === 'master' || level === 'graduate' || level.includes('master') && !level.includes('licence') && !level.includes('doctorat') || level.includes('graduate') && !level.includes('undergraduate') && !level.includes('phd');\n        });\n        const doctoratScholarships = allScholarships.filter(scholarship => {\n          var _scholarship$level3;\n          const level = ((_scholarship$level3 = scholarship.level) === null || _scholarship$level3 === void 0 ? void 0 : _scholarship$level3.toLowerCase()) || '';\n          return level === 'doctorat' || level === 'phd' || level === 'doctorate' || level.includes('doctorat') && !level.includes('licence') && !level.includes('master') || level.includes('phd') && !level.includes('undergraduate') && !level.includes('graduate');\n        });\n        setScholarships({\n          licence: licenceScholarships,\n          master: masterScholarships,\n          doctorat: doctoratScholarships\n        });\n      } catch (error) {\n        console.error('Error fetching scholarships by level:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchScholarshipsByLevel();\n  }, []);\n\n  // Get active scholarships based on tab\n  const getActiveScholarships = () => {\n    switch (activeTab) {\n      case 'licence':\n        return scholarships.licence;\n      case 'master':\n        return scholarships.master;\n      case 'doctorat':\n        return scholarships.doctorat;\n      default:\n        return scholarships.licence;\n    }\n  };\n  const activeScholarships = getActiveScholarships();\n\n  // Tab configuration\n  const tabs = [{\n    id: 'licence',\n    title: 'Licence',\n    description: 'Bourses pour les étudiants de premier cycle universitaire (Bac+3)',\n    color: 'blue',\n    icon: /*#__PURE__*/_jsxDEV(GraduationCap, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this),\n    benefits: ['Frais de scolarité', 'Allocation mensuelle', 'Frais de voyage'],\n    bgClass: 'from-blue-500 to-blue-700'\n  }, {\n    id: 'master',\n    title: 'Master',\n    description: 'Bourses pour les étudiants de deuxième cycle universitaire (Bac+5)',\n    color: 'purple',\n    icon: /*#__PURE__*/_jsxDEV(GraduationCap, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this),\n    benefits: ['Frais de scolarité', 'Allocation mensuelle', 'Assurance santé'],\n    bgClass: 'from-purple-500 to-purple-700'\n  }, {\n    id: 'doctorat',\n    title: 'Doctorat',\n    description: 'Bourses pour les étudiants de troisième cycle universitaire (Bac+8)',\n    color: 'indigo',\n    icon: /*#__PURE__*/_jsxDEV(GraduationCap, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 13\n    }, this),\n    benefits: ['Frais de scolarité', 'Allocation de recherche', 'Conférences internationales'],\n    bgClass: 'from-indigo-500 to-indigo-700'\n  }];\n\n  // Get active tab data\n  const activeTabData = tabs.find(tab => tab.id === activeTab) || tabs[0];\n\n  // Handle scholarship click\n  const handleScholarshipClick = (id, slug) => {\n    // Navigate to scholarship details page using slug if available\n    if (slug) {\n      window.location.href = `/bourse/${slug}`;\n    } else {\n      window.location.href = `/scholarships/${id}`;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-8 bg-white overflow-hidden\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900 mb-3\",\n          children: \"Niveaux d'\\xC9tudes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n          children: \"Trouvez des bourses adapt\\xE9es \\xE0 votre parcours acad\\xE9mique\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: tabsRef,\n        className: \"flex flex-wrap justify-center gap-3 mb-6\",\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.id),\n          className: `flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${activeTab === tab.id ? `bg-${tab.color}-600 text-white shadow-md shadow-${tab.color}-500/30 scale-105` : `bg-white text-gray-700 hover:bg-${tab.color}-50 hover:text-${tab.color}-600`}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `flex items-center justify-center w-6 h-6 rounded-full ${activeTab === tab.id ? `bg-${tab.color}-500/30 text-white` : `bg-${tab.color}-100 text-${tab.color}-600`} mr-2`,\n            children: tab.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), tab.title]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `relative rounded-xl overflow-hidden mb-5 bg-gradient-to-r ${activeTabData.bgClass}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-full h-full\",\n              viewBox: \"0 0 100 100\",\n              preserveAspectRatio: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M0,0 L100,0 L100,100 L0,100 Z\",\n                fill: \"url(#pattern)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n              children: /*#__PURE__*/_jsxDEV(\"pattern\", {\n                id: \"pattern\",\n                x: \"0\",\n                y: \"0\",\n                width: \"10\",\n                height: \"10\",\n                patternUnits: \"userSpaceOnUse\",\n                children: /*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"5\",\n                  cy: \"5\",\n                  r: \"1\",\n                  fill: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative py-4 px-6 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0 mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm\",\n                  children: activeTabData.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-bold\",\n                  children: [\"Bourses de \", activeTabData.title]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/90 text-xs\",\n                  children: activeTabData.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: [...Array(6)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-md overflow-hidden animate-pulse\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"aspect-[16/9] bg-gray-200\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-5 bg-gray-200 rounded w-3/4 mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-1/2 mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-full mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-10 bg-gray-100 rounded w-full mt-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this) : activeScholarships.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: activeScholarships.slice(0, 6).map((scholarship, index) => /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n          id: scholarship.id,\n          title: scholarship.title,\n          thumbnail: scholarship.thumbnail,\n          deadline: scholarship.deadline,\n          isOpen: scholarship.isOpen,\n          level: scholarship.level,\n          fundingSource: scholarship.fundingSource,\n          country: scholarship.country,\n          onClick: handleScholarshipClick,\n          index: index\n        }, scholarship.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12 bg-white rounded-2xl shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"mx-auto h-12 w-12 text-gray-400\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 1,\n            d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-lg font-medium text-gray-900\",\n          children: \"Aucune bourse trouv\\xE9e\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-gray-500\",\n          children: \"Aucune bourse n'est disponible pour ce niveau d'\\xE9tudes.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: `/scholarships?level=${activeTabData.title}`,\n          className: `inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-xl text-white shadow-md transition-colors duration-300 ${activeTab === 'licence' ? 'bg-blue-600 hover:bg-blue-700' : activeTab === 'master' ? 'bg-purple-600 hover:bg-purple-700' : 'bg-indigo-600 hover:bg-indigo-700'}`,\n          children: [\"Voir toutes les bourses de \", activeTabData.title, /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4 ml-2\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedStudyLevelSection, \"EwsoWkRtcBpcuemfdqXytt0JXfw=\");\n_c = EnhancedStudyLevelSection;\nexport default EnhancedStudyLevelSection;\nvar _c;\n$RefreshReg$(_c, \"EnhancedStudyLevelSection\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Link", "GraduationCap", "EnhancedScholarshipCard", "scholarshipService", "jsxDEV", "_jsxDEV", "EnhancedStudyLevelSection", "_s", "activeTab", "setActiveTab", "tabsRef", "loading", "setLoading", "scholarships", "setScholarships", "licence", "master", "doctorat", "fetchScholarshipsByLevel", "allScholarships", "getAllScholarships", "licenceScholarships", "filter", "scholarship", "_scholarship$level", "level", "toLowerCase", "includes", "masterScholarships", "_scholarship$level2", "doctoratScholarships", "_scholarship$level3", "error", "console", "getActiveScholarships", "activeScholarships", "tabs", "id", "title", "description", "color", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "benefits", "bgClass", "activeTabData", "find", "tab", "handleScholarshipClick", "slug", "window", "location", "href", "children", "ref", "map", "onClick", "viewBox", "preserveAspectRatio", "d", "fill", "x", "y", "width", "height", "patternUnits", "cx", "cy", "r", "Array", "_", "index", "length", "slice", "thumbnail", "deadline", "isOpen", "fundingSource", "country", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "to", "xmlns", "fillRule", "clipRule", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { GraduationCap } from './icons/index';\nimport EnhancedScholarshipCard from './EnhancedScholarshipCard';\nimport { Scholarship } from './ScholarshipGrid';\nimport scholarshipService from '../services/scholarshipService';\n\ninterface EnhancedStudyLevelSectionProps {\n  // You can add props here if needed\n}\n\nconst EnhancedStudyLevelSection: React.FC<EnhancedStudyLevelSectionProps> = () => {\n  const [activeTab, setActiveTab] = useState('licence');\n  const tabsRef = useRef<HTMLDivElement>(null);\n  const [loading, setLoading] = useState(true);\n  const [scholarships, setScholarships] = useState<{\n    licence: Scholarship[];\n    master: Scholarship[];\n    doctorat: Scholarship[];\n  }>({\n    licence: [],\n    master: [],\n    doctorat: []\n  });\n\n  // Fetch scholarships by level\n  useEffect(() => {\n    const fetchScholarshipsByLevel = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch all scholarships\n        const allScholarships = await scholarshipService.getAllScholarships();\n\n        // Filter scholarships by level more precisely\n        const licenceScholarships = allScholarships.filter(scholarship => {\n          const level = scholarship.level?.toLowerCase() || '';\n          return level === 'licence' ||\n                 level === 'undergraduate' ||\n                 level === 'bachelor' ||\n                 (level.includes('licence') && !level.includes('master') && !level.includes('doctorat')) ||\n                 (level.includes('undergraduate') && !level.includes('graduate') && !level.includes('phd'));\n        });\n\n        const masterScholarships = allScholarships.filter(scholarship => {\n          const level = scholarship.level?.toLowerCase() || '';\n          return level === 'master' ||\n                 level === 'graduate' ||\n                 (level.includes('master') && !level.includes('licence') && !level.includes('doctorat')) ||\n                 (level.includes('graduate') && !level.includes('undergraduate') && !level.includes('phd'));\n        });\n\n        const doctoratScholarships = allScholarships.filter(scholarship => {\n          const level = scholarship.level?.toLowerCase() || '';\n          return level === 'doctorat' ||\n                 level === 'phd' ||\n                 level === 'doctorate' ||\n                 (level.includes('doctorat') && !level.includes('licence') && !level.includes('master')) ||\n                 (level.includes('phd') && !level.includes('undergraduate') && !level.includes('graduate'));\n        });\n\n        setScholarships({\n          licence: licenceScholarships,\n          master: masterScholarships,\n          doctorat: doctoratScholarships\n        });\n      } catch (error) {\n        console.error('Error fetching scholarships by level:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchScholarshipsByLevel();\n  }, []);\n\n  // Get active scholarships based on tab\n  const getActiveScholarships = () => {\n    switch (activeTab) {\n      case 'licence':\n        return scholarships.licence;\n      case 'master':\n        return scholarships.master;\n      case 'doctorat':\n        return scholarships.doctorat;\n      default:\n        return scholarships.licence;\n    }\n  };\n\n  const activeScholarships = getActiveScholarships();\n\n  // Tab configuration\n  const tabs = [\n    {\n      id: 'licence',\n      title: 'Licence',\n      description: 'Bourses pour les étudiants de premier cycle universitaire (Bac+3)',\n      color: 'blue',\n      icon: <GraduationCap className=\"w-5 h-5\" />,\n      benefits: ['Frais de scolarité', 'Allocation mensuelle', 'Frais de voyage'],\n      bgClass: 'from-blue-500 to-blue-700'\n    },\n    {\n      id: 'master',\n      title: 'Master',\n      description: 'Bourses pour les étudiants de deuxième cycle universitaire (Bac+5)',\n      color: 'purple',\n      icon: <GraduationCap className=\"w-5 h-5\" />,\n      benefits: ['Frais de scolarité', 'Allocation mensuelle', 'Assurance santé'],\n      bgClass: 'from-purple-500 to-purple-700'\n    },\n    {\n      id: 'doctorat',\n      title: 'Doctorat',\n      description: 'Bourses pour les étudiants de troisième cycle universitaire (Bac+8)',\n      color: 'indigo',\n      icon: <GraduationCap className=\"w-5 h-5\" />,\n      benefits: ['Frais de scolarité', 'Allocation de recherche', 'Conférences internationales'],\n      bgClass: 'from-indigo-500 to-indigo-700'\n    }\n  ];\n\n  // Get active tab data\n  const activeTabData = tabs.find(tab => tab.id === activeTab) || tabs[0];\n\n  // Handle scholarship click\n  const handleScholarshipClick = (id: number, slug?: string) => {\n    // Navigate to scholarship details page using slug if available\n    if (slug) {\n      window.location.href = `/bourse/${slug}`;\n    } else {\n      window.location.href = `/scholarships/${id}`;\n    }\n  };\n\n  return (\n    <section className=\"py-8 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section header - Professional & Clean */}\n        <div className=\"text-center mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-3\">\n            Niveaux d'Études\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Trouvez des bourses adaptées à votre parcours académique\n          </p>\n        </div>\n\n        {/* Interactive tabs */}\n        <div ref={tabsRef} className=\"flex flex-wrap justify-center gap-3 mb-6\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${\n                activeTab === tab.id\n                  ? `bg-${tab.color}-600 text-white shadow-md shadow-${tab.color}-500/30 scale-105`\n                  : `bg-white text-gray-700 hover:bg-${tab.color}-50 hover:text-${tab.color}-600`\n              }`}\n            >\n              <span className={`flex items-center justify-center w-6 h-6 rounded-full ${\n                activeTab === tab.id ? `bg-${tab.color}-500/30 text-white` : `bg-${tab.color}-100 text-${tab.color}-600`\n              } mr-2`}>\n                {tab.icon}\n              </span>\n              {tab.title}\n            </button>\n          ))}\n        </div>\n\n        {/* Tab content with header */}\n        <div className=\"mb-5\">\n          <div className={`relative rounded-xl overflow-hidden mb-5 bg-gradient-to-r ${activeTabData.bgClass}`}>\n            <div className=\"absolute inset-0 opacity-20\">\n              <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\" preserveAspectRatio=\"none\">\n                <path d=\"M0,0 L100,0 L100,100 L0,100 Z\" fill=\"url(#pattern)\" />\n              </svg>\n              <defs>\n                <pattern id=\"pattern\" x=\"0\" y=\"0\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\">\n                  <circle cx=\"5\" cy=\"5\" r=\"1\" fill=\"white\" />\n                </pattern>\n              </defs>\n            </div>\n            <div className=\"relative py-4 px-6 text-white\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0 mr-3\">\n                  <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm\">\n                    {activeTabData.icon}\n                  </div>\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-bold\">Bourses de {activeTabData.title}</h3>\n                  <p className=\"text-white/90 text-xs\">\n                    {activeTabData.description}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scholarships grid - 3x2 grid */}\n        {loading ? (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[...Array(6)].map((_, index) => (\n              <div key={index} className=\"bg-white rounded-2xl shadow-md overflow-hidden animate-pulse\">\n                <div className=\"aspect-[16/9] bg-gray-200\"></div>\n                <div className=\"p-5\">\n                  <div className=\"h-5 bg-gray-200 rounded w-3/4 mb-3\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-3\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-full mb-3\"></div>\n                  <div className=\"h-10 bg-gray-100 rounded w-full mt-4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : activeScholarships.length > 0 ? (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {activeScholarships.slice(0, 6).map((scholarship, index) => (\n              <EnhancedScholarshipCard\n                key={scholarship.id}\n                id={scholarship.id}\n                title={scholarship.title}\n                thumbnail={scholarship.thumbnail}\n                deadline={scholarship.deadline}\n                isOpen={scholarship.isOpen}\n                level={scholarship.level}\n                fundingSource={scholarship.fundingSource}\n                country={scholarship.country}\n                onClick={handleScholarshipClick}\n                index={index}\n              />\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12 bg-white rounded-2xl shadow-sm\">\n            <svg className=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">Aucune bourse trouvée</h3>\n            <p className=\"mt-1 text-gray-500\">Aucune bourse n'est disponible pour ce niveau d'études.</p>\n          </div>\n        )}\n\n        {/* Call to action */}\n        <div className=\"mt-6 flex justify-center\">\n          <Link\n            to={`/scholarships?level=${activeTabData.title}`}\n            className={`inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-xl text-white shadow-md transition-colors duration-300 ${\n              activeTab === 'licence' ? 'bg-blue-600 hover:bg-blue-700' :\n              activeTab === 'master' ? 'bg-purple-600 hover:bg-purple-700' :\n              'bg-indigo-600 hover:bg-indigo-700'\n            }`}\n          >\n            Voir toutes les bourses de {activeTabData.title}\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default EnhancedStudyLevelSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,aAAa,QAAQ,eAAe;AAC7C,OAAOC,uBAAuB,MAAM,2BAA2B;AAE/D,OAAOC,kBAAkB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMhE,MAAMC,yBAAmE,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAMa,OAAO,GAAGZ,MAAM,CAAiB,IAAI,CAAC;EAC5C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAI7C;IACDkB,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACAlB,SAAS,CAAC,MAAM;IACd,MAAMmB,wBAAwB,GAAG,MAAAA,CAAA,KAAY;MAC3C,IAAI;QACFN,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMO,eAAe,GAAG,MAAMhB,kBAAkB,CAACiB,kBAAkB,CAAC,CAAC;;QAErE;QACA,MAAMC,mBAAmB,GAAGF,eAAe,CAACG,MAAM,CAACC,WAAW,IAAI;UAAA,IAAAC,kBAAA;UAChE,MAAMC,KAAK,GAAG,EAAAD,kBAAA,GAAAD,WAAW,CAACE,KAAK,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBE,WAAW,CAAC,CAAC,KAAI,EAAE;UACpD,OAAOD,KAAK,KAAK,SAAS,IACnBA,KAAK,KAAK,eAAe,IACzBA,KAAK,KAAK,UAAU,IACnBA,KAAK,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAE,IACtFF,KAAK,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,KAAK,CAAE;QACnG,CAAC,CAAC;QAEF,MAAMC,kBAAkB,GAAGT,eAAe,CAACG,MAAM,CAACC,WAAW,IAAI;UAAA,IAAAM,mBAAA;UAC/D,MAAMJ,KAAK,GAAG,EAAAI,mBAAA,GAAAN,WAAW,CAACE,KAAK,cAAAI,mBAAA,uBAAjBA,mBAAA,CAAmBH,WAAW,CAAC,CAAC,KAAI,EAAE;UACpD,OAAOD,KAAK,KAAK,QAAQ,IAClBA,KAAK,KAAK,UAAU,IACnBA,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAE,IACtFF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,KAAK,CAAE;QACnG,CAAC,CAAC;QAEF,MAAMG,oBAAoB,GAAGX,eAAe,CAACG,MAAM,CAACC,WAAW,IAAI;UAAA,IAAAQ,mBAAA;UACjE,MAAMN,KAAK,GAAG,EAAAM,mBAAA,GAAAR,WAAW,CAACE,KAAK,cAAAM,mBAAA,uBAAjBA,mBAAA,CAAmBL,WAAW,CAAC,CAAC,KAAI,EAAE;UACpD,OAAOD,KAAK,KAAK,UAAU,IACpBA,KAAK,KAAK,KAAK,IACfA,KAAK,KAAK,WAAW,IACpBA,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAE,IACtFF,KAAK,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAI,CAACF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAE;QACnG,CAAC,CAAC;QAEFb,eAAe,CAAC;UACdC,OAAO,EAAEM,mBAAmB;UAC5BL,MAAM,EAAEY,kBAAkB;UAC1BX,QAAQ,EAAEa;QACZ,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,SAAS;QACRpB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDM,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,QAAQ1B,SAAS;MACf,KAAK,SAAS;QACZ,OAAOK,YAAY,CAACE,OAAO;MAC7B,KAAK,QAAQ;QACX,OAAOF,YAAY,CAACG,MAAM;MAC5B,KAAK,UAAU;QACb,OAAOH,YAAY,CAACI,QAAQ;MAC9B;QACE,OAAOJ,YAAY,CAACE,OAAO;IAC/B;EACF,CAAC;EAED,MAAMoB,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC;;EAElD;EACA,MAAME,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mEAAmE;IAChFC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEpC,OAAA,CAACJ,aAAa;MAACyC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3CC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,iBAAiB,CAAC;IAC3EC,OAAO,EAAE;EACX,CAAC,EACD;IACEX,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,oEAAoE;IACjFC,KAAK,EAAE,QAAQ;IACfC,IAAI,eAAEpC,OAAA,CAACJ,aAAa;MAACyC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3CC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,iBAAiB,CAAC;IAC3EC,OAAO,EAAE;EACX,CAAC,EACD;IACEX,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,qEAAqE;IAClFC,KAAK,EAAE,QAAQ;IACfC,IAAI,eAAEpC,OAAA,CAACJ,aAAa;MAACyC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3CC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,yBAAyB,EAAE,6BAA6B,CAAC;IAC1FC,OAAO,EAAE;EACX,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAGb,IAAI,CAACc,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACd,EAAE,KAAK7B,SAAS,CAAC,IAAI4B,IAAI,CAAC,CAAC,CAAC;;EAEvE;EACA,MAAMgB,sBAAsB,GAAGA,CAACf,EAAU,EAAEgB,IAAa,KAAK;IAC5D;IACA,IAAIA,IAAI,EAAE;MACRC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAWH,IAAI,EAAE;IAC1C,CAAC,MAAM;MACLC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,iBAAiBnB,EAAE,EAAE;IAC9C;EACF,CAAC;EAED,oBACEhC,OAAA;IAASqC,SAAS,EAAC,+BAA+B;IAAAe,QAAA,eAChDpD,OAAA;MAAKqC,SAAS,EAAC,wCAAwC;MAAAe,QAAA,gBAErDpD,OAAA;QAAKqC,SAAS,EAAC,kBAAkB;QAAAe,QAAA,gBAC/BpD,OAAA;UAAIqC,SAAS,EAAC,uCAAuC;UAAAe,QAAA,EAAC;QAEtD;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzC,OAAA;UAAGqC,SAAS,EAAC,yCAAyC;UAAAe,QAAA,EAAC;QAEvD;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNzC,OAAA;QAAKqD,GAAG,EAAEhD,OAAQ;QAACgC,SAAS,EAAC,0CAA0C;QAAAe,QAAA,EACpErB,IAAI,CAACuB,GAAG,CAAER,GAAG,iBACZ9C,OAAA;UAEEuD,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAAC0C,GAAG,CAACd,EAAE,CAAE;UACpCK,SAAS,EAAE,0FACTlC,SAAS,KAAK2C,GAAG,CAACd,EAAE,GAChB,MAAMc,GAAG,CAACX,KAAK,oCAAoCW,GAAG,CAACX,KAAK,mBAAmB,GAC/E,mCAAmCW,GAAG,CAACX,KAAK,kBAAkBW,GAAG,CAACX,KAAK,MAAM,EAChF;UAAAiB,QAAA,gBAEHpD,OAAA;YAAMqC,SAAS,EAAE,yDACflC,SAAS,KAAK2C,GAAG,CAACd,EAAE,GAAG,MAAMc,GAAG,CAACX,KAAK,oBAAoB,GAAG,MAAMW,GAAG,CAACX,KAAK,aAAaW,GAAG,CAACX,KAAK,MAAM,OAClG;YAAAiB,QAAA,EACLN,GAAG,CAACV;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACNK,GAAG,CAACb,KAAK;QAAA,GAbLa,GAAG,CAACd,EAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcL,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzC,OAAA;QAAKqC,SAAS,EAAC,MAAM;QAAAe,QAAA,eACnBpD,OAAA;UAAKqC,SAAS,EAAE,6DAA6DO,aAAa,CAACD,OAAO,EAAG;UAAAS,QAAA,gBACnGpD,OAAA;YAAKqC,SAAS,EAAC,6BAA6B;YAAAe,QAAA,gBAC1CpD,OAAA;cAAKqC,SAAS,EAAC,eAAe;cAACmB,OAAO,EAAC,aAAa;cAACC,mBAAmB,EAAC,MAAM;cAAAL,QAAA,eAC7EpD,OAAA;gBAAM0D,CAAC,EAAC,+BAA+B;gBAACC,IAAI,EAAC;cAAe;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNzC,OAAA;cAAAoD,QAAA,eACEpD,OAAA;gBAASgC,EAAE,EAAC,SAAS;gBAAC4B,CAAC,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAACC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,YAAY,EAAC,gBAAgB;gBAAAZ,QAAA,eACpFpD,OAAA;kBAAQiE,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACR,IAAI,EAAC;gBAAO;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzC,OAAA;YAAKqC,SAAS,EAAC,+BAA+B;YAAAe,QAAA,eAC5CpD,OAAA;cAAKqC,SAAS,EAAC,mBAAmB;cAAAe,QAAA,gBAChCpD,OAAA;gBAAKqC,SAAS,EAAC,oBAAoB;gBAAAe,QAAA,eACjCpD,OAAA;kBAAKqC,SAAS,EAAC,sFAAsF;kBAAAe,QAAA,EAClGR,aAAa,CAACR;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzC,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAIqC,SAAS,EAAC,mBAAmB;kBAAAe,QAAA,GAAC,aAAW,EAACR,aAAa,CAACX,KAAK;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvEzC,OAAA;kBAAGqC,SAAS,EAAC,uBAAuB;kBAAAe,QAAA,EACjCR,aAAa,CAACV;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLnC,OAAO,gBACNN,OAAA;QAAKqC,SAAS,EAAC,sDAAsD;QAAAe,QAAA,EAClE,CAAC,GAAGgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACd,GAAG,CAAC,CAACe,CAAC,EAAEC,KAAK,kBAC1BtE,OAAA;UAAiBqC,SAAS,EAAC,8DAA8D;UAAAe,QAAA,gBACvFpD,OAAA;YAAKqC,SAAS,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDzC,OAAA;YAAKqC,SAAS,EAAC,KAAK;YAAAe,QAAA,gBAClBpD,OAAA;cAAKqC,SAAS,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DzC,OAAA;cAAKqC,SAAS,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DzC,OAAA;cAAKqC,SAAS,EAAC;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DzC,OAAA;cAAKqC,SAAS,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA,GAPE6B,KAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,GACJX,kBAAkB,CAACyC,MAAM,GAAG,CAAC,gBAC/BvE,OAAA;QAAKqC,SAAS,EAAC,sDAAsD;QAAAe,QAAA,EAClEtB,kBAAkB,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClB,GAAG,CAAC,CAACpC,WAAW,EAAEoD,KAAK,kBACrDtE,OAAA,CAACH,uBAAuB;UAEtBmC,EAAE,EAAEd,WAAW,CAACc,EAAG;UACnBC,KAAK,EAAEf,WAAW,CAACe,KAAM;UACzBwC,SAAS,EAAEvD,WAAW,CAACuD,SAAU;UACjCC,QAAQ,EAAExD,WAAW,CAACwD,QAAS;UAC/BC,MAAM,EAAEzD,WAAW,CAACyD,MAAO;UAC3BvD,KAAK,EAAEF,WAAW,CAACE,KAAM;UACzBwD,aAAa,EAAE1D,WAAW,CAAC0D,aAAc;UACzCC,OAAO,EAAE3D,WAAW,CAAC2D,OAAQ;UAC7BtB,OAAO,EAAER,sBAAuB;UAChCuB,KAAK,EAAEA;QAAM,GAVRpD,WAAW,CAACc,EAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWpB,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENzC,OAAA;QAAKqC,SAAS,EAAC,kDAAkD;QAAAe,QAAA,gBAC/DpD,OAAA;UAAKqC,SAAS,EAAC,iCAAiC;UAACsB,IAAI,EAAC,MAAM;UAACH,OAAO,EAAC,WAAW;UAACsB,MAAM,EAAC,cAAc;UAAA1B,QAAA,eACpGpD,OAAA;YAAM+E,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACvB,CAAC,EAAC;UAAoF;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzJ,CAAC,eACNzC,OAAA;UAAIqC,SAAS,EAAC,wCAAwC;UAAAe,QAAA,EAAC;QAAqB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFzC,OAAA;UAAGqC,SAAS,EAAC,oBAAoB;UAAAe,QAAA,EAAC;QAAuD;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CACN,eAGDzC,OAAA;QAAKqC,SAAS,EAAC,0BAA0B;QAAAe,QAAA,eACvCpD,OAAA,CAACL,IAAI;UACHuF,EAAE,EAAE,uBAAuBtC,aAAa,CAACX,KAAK,EAAG;UACjDI,SAAS,EAAE,mJACTlC,SAAS,KAAK,SAAS,GAAG,+BAA+B,GACzDA,SAAS,KAAK,QAAQ,GAAG,mCAAmC,GAC5D,mCAAmC,EAClC;UAAAiD,QAAA,GACJ,6BAC4B,EAACR,aAAa,CAACX,KAAK,eAC/CjC,OAAA;YAAKmF,KAAK,EAAC,4BAA4B;YAAC9C,SAAS,EAAC,cAAc;YAACmB,OAAO,EAAC,WAAW;YAACG,IAAI,EAAC,cAAc;YAAAP,QAAA,eACtGpD,OAAA;cAAMoF,QAAQ,EAAC,SAAS;cAAC1B,CAAC,EAAC,yIAAyI;cAAC2B,QAAQ,EAAC;YAAS;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACvC,EAAA,CA7PID,yBAAmE;AAAAqF,EAAA,GAAnErF,yBAAmE;AA+PzE,eAAeA,yBAAyB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}