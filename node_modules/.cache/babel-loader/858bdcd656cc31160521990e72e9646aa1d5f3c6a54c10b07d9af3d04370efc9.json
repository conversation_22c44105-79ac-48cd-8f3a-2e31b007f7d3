{"ast": null, "code": "/**\n * Professional Sidebar Data Service\n * Unified service for fetching sidebar data across all dedicated pages\n * Following industry standards like greatyop.com\n */\n\nclass SidebarService {\n  constructor() {\n    this.baseUrl = '/api';\n    this.cache = new Map();\n    this.DEFAULT_TTL = 5 * 60 * 1000;\n    // 5 minutes\n    this.RELATED_ITEMS_TTL = 10 * 60 * 1000;\n    // 10 minutes (changes less frequently)\n    this.LATEST_ITEMS_TTL = 2 * 60 * 1000;\n  }\n  // 2 minutes (changes more frequently)\n\n  /**\n   * Fetch unified sidebar data with intelligent caching and parallel requests\n   */\n  async fetchSidebarData(config) {\n    try {\n      // Create cache keys for different data types\n      const relatedKey = this.getCacheKey('related', config);\n      const latestKey = this.getCacheKey('latest', config);\n      const statsKey = config.currentItem ? this.getCacheKey('stats', config) : null;\n\n      // Check cache first, then fetch missing data in parallel\n      const promises = [];\n\n      // Related items (cached longer as they change less frequently)\n      const cachedRelated = this.getFromCache(relatedKey);\n      if (cachedRelated) {\n        promises.push(Promise.resolve(cachedRelated));\n      } else {\n        promises.push(this.fetchRelatedItems(config).then(data => {\n          this.setCache(relatedKey, data, this.RELATED_ITEMS_TTL);\n          return data;\n        }));\n      }\n\n      // Latest items (cached shorter as they change more frequently)\n      const cachedLatest = this.getFromCache(latestKey);\n      if (cachedLatest) {\n        promises.push(Promise.resolve(cachedLatest));\n      } else {\n        promises.push(this.fetchLatestItems(config).then(data => {\n          this.setCache(latestKey, data, this.LATEST_ITEMS_TTL);\n          return data;\n        }));\n      }\n\n      // Statistics (if needed)\n      if (statsKey) {\n        const cachedStats = this.getFromCache(statsKey);\n        if (cachedStats) {\n          promises.push(Promise.resolve(cachedStats));\n        } else {\n          promises.push(this.fetchStatistics(config).then(data => {\n            this.setCache(statsKey, data, this.DEFAULT_TTL);\n            return data;\n          }));\n        }\n      }\n      const results = await Promise.all(promises);\n      const [relatedItems, latestItems, statistics] = results;\n      return {\n        relatedItems: relatedItems || [],\n        latestItems: latestItems || [],\n        statistics: statistics || null\n      };\n    } catch (error) {\n      console.error('Error fetching sidebar data:', error);\n      // Return cached data if available, otherwise empty data\n      return this.getFallbackData(config);\n    }\n  }\n\n  /**\n   * Generate cache key for different data types\n   */\n  getCacheKey(type, config) {\n    const parts = [type, config.type];\n    if (config.currentItem) parts.push(config.currentItem);\n    if (config.excludeId) parts.push(`exclude-${config.excludeId}`);\n    if (config.limit) parts.push(`limit-${config.limit}`);\n    return parts.join(':');\n  }\n\n  /**\n   * Get data from cache if valid\n   */\n  getFromCache(key) {\n    const cached = this.cache.get(key);\n    if (cached && Date.now() - cached.timestamp < cached.ttl) {\n      return cached.data;\n    }\n    if (cached) {\n      this.cache.delete(key); // Remove expired cache\n    }\n    return null;\n  }\n\n  /**\n   * Set data in cache with TTL\n   */\n  setCache(key, data, ttl) {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl\n    });\n  }\n\n  /**\n   * Get fallback data from cache (even if expired) or return empty data\n   */\n  getFallbackData(config) {\n    var _this$cache$get, _this$cache$get2;\n    const relatedKey = this.getCacheKey('related', config);\n    const latestKey = this.getCacheKey('latest', config);\n    const fallbackRelated = ((_this$cache$get = this.cache.get(relatedKey)) === null || _this$cache$get === void 0 ? void 0 : _this$cache$get.data) || [];\n    const fallbackLatest = ((_this$cache$get2 = this.cache.get(latestKey)) === null || _this$cache$get2 === void 0 ? void 0 : _this$cache$get2.data) || [];\n    return {\n      relatedItems: fallbackRelated,\n      latestItems: fallbackLatest\n    };\n  }\n\n  /**\n   * Fetch related items with retry logic and better error handling\n   */\n  async fetchRelatedItems(config) {\n    return this.fetchWithRetry(async () => {\n      let endpoint = '';\n      const params = new URLSearchParams();\n      if (config.limit) {\n        params.append('limit', config.limit.toString());\n      }\n      if (config.currentItem) {\n        params.append(`exclude${this.getExcludeParam(config.type)}`, config.currentItem);\n      }\n      switch (config.type) {\n        case 'countries':\n          endpoint = `${this.baseUrl}/countries/sidebar`;\n          break;\n        case 'levels':\n          endpoint = `${this.baseUrl}/scholarships/levels`;\n          break;\n        case 'opportunities':\n          endpoint = `${this.baseUrl}/opportunities/types-sidebar`;\n          break;\n        default:\n          throw new Error(`Unknown config type: ${config.type}`);\n      }\n      const response = await this.fetchWithTimeout(`${endpoint}?${params}`, 5000);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n      const data = await response.json();\n      return data.data || [];\n    }, `related-${config.type}`);\n  }\n\n  /**\n   * Fetch latest items with enhanced error handling\n   */\n  async fetchLatestItems(config) {\n    return this.fetchWithRetry(async () => {\n      const params = new URLSearchParams();\n      params.append('limit', '6');\n      if (config.excludeId) {\n        params.append('excludeId', config.excludeId.toString());\n      }\n      let endpoint = '';\n      if (config.type === 'opportunities') {\n        endpoint = `${this.baseUrl}/opportunities/latest`;\n      } else {\n        endpoint = `${this.baseUrl}/scholarships/latest`;\n      }\n      const response = await this.fetchWithTimeout(`${endpoint}?${params}`, 5000);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n      const data = await response.json();\n      return data.data || [];\n    }, `latest-${config.type}`);\n  }\n\n  /**\n   * Fetch with timeout to prevent hanging requests\n   */\n  async fetchWithTimeout(url, timeout) {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), timeout);\n    try {\n      const response = await fetch(url, {\n        signal: controller.signal,\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      clearTimeout(timeoutId);\n      return response;\n    } catch (error) {\n      clearTimeout(timeoutId);\n      throw error;\n    }\n  }\n\n  /**\n   * Retry logic for failed requests\n   */\n  async fetchWithRetry(fetchFn, operation, maxRetries = 2) {\n    let lastError;\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        return await fetchFn();\n      } catch (error) {\n        lastError = error;\n        console.warn(`${operation} attempt ${attempt + 1} failed:`, error);\n        if (attempt < maxRetries) {\n          // Exponential backoff: 500ms, 1000ms, 2000ms\n          const delay = 500 * Math.pow(2, attempt);\n          await new Promise(resolve => setTimeout(resolve, delay));\n        }\n      }\n    }\n    console.error(`${operation} failed after ${maxRetries + 1} attempts:`, lastError);\n    throw lastError;\n  }\n\n  /**\n   * Fetch statistics with enhanced error handling\n   */\n  async fetchStatistics(config) {\n    if (!config.currentItem) return null;\n    return this.fetchWithRetry(async () => {\n      let endpoint = '';\n      switch (config.type) {\n        case 'countries':\n          endpoint = `${this.baseUrl}/countries/${encodeURIComponent(config.currentItem)}/statistics`;\n          break;\n        case 'levels':\n          // Level statistics can be computed from the main data\n          return null;\n        case 'opportunities':\n          // Opportunity type statistics can be computed from the main data\n          return null;\n      }\n      if (endpoint) {\n        const response = await this.fetchWithTimeout(endpoint, 5000);\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const data = await response.json();\n        return data.data;\n      }\n      return null;\n    }, `stats-${config.type}-${config.currentItem}`);\n  }\n\n  /**\n   * Clear cache for specific type or all cache\n   */\n  clearCache(type) {\n    if (type) {\n      // Clear cache entries that start with the type\n      for (const [key] of this.cache) {\n        if (key.startsWith(type)) {\n          this.cache.delete(key);\n        }\n      }\n    } else {\n      // Clear all cache\n      this.cache.clear();\n    }\n  }\n\n  /**\n   * Get cache statistics for debugging\n   */\n  getCacheStats() {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n\n  /**\n   * Preload data for better performance\n   */\n  async preloadData(configs) {\n    const promises = configs.map(config => this.fetchSidebarData(config).catch(error => {\n      console.warn('Preload failed for config:', config, error);\n    }));\n    await Promise.allSettled(promises);\n  }\n\n  /**\n   * Get the correct exclude parameter name based on type\n   */\n  getExcludeParam(type) {\n    switch (type) {\n      case 'countries':\n        return 'Country';\n      case 'levels':\n        return 'Level';\n      case 'opportunities':\n        return 'Type';\n      default:\n        return 'Item';\n    }\n  }\n\n  /**\n   * Get country flag emoji (utility function)\n   */\n  getCountryFlag(country) {\n    const flagMap = {\n      'France': '🇫🇷',\n      'Germany': '🇩🇪',\n      'United Kingdom': '🇬🇧',\n      'United States': '🇺🇸',\n      'Canada': '🇨🇦',\n      'Australia': '🇦🇺',\n      'Netherlands': '🇳🇱',\n      'Sweden': '🇸🇪',\n      'Norway': '🇳🇴',\n      'Denmark': '🇩🇰',\n      'Switzerland': '🇨🇭',\n      'Belgium': '🇧🇪',\n      'Austria': '🇦🇹',\n      'Italy': '🇮🇹',\n      'Spain': '🇪🇸',\n      'Japan': '🇯🇵',\n      'South Korea': '🇰🇷',\n      'Singapore': '🇸🇬',\n      'New Zealand': '🇳🇿',\n      'Finland': '🇫🇮'\n    };\n    return flagMap[country] || '🌍';\n  }\n\n  /**\n   * Get level icon (utility function)\n   */\n  getLevelIcon(level) {\n    const iconMap = {\n      'Bachelor': '🎓',\n      'Master': '📚',\n      'PhD': '🔬',\n      'Postdoc': '👨‍🔬',\n      'Undergraduate': '🎓',\n      'Graduate': '📚',\n      'Doctorate': '🔬'\n    };\n    return iconMap[level] || '📖';\n  }\n\n  /**\n   * Get opportunity type icon (utility function)\n   */\n  getOpportunityIcon(type) {\n    const iconMap = {\n      'internship': '💼',\n      'training': '🎯',\n      'conference': '🎤',\n      'workshop': '🛠️',\n      'competition': '🏆'\n    };\n    return iconMap[type] || '🔗';\n  }\n}\nexport const sidebarService = new SidebarService();\nexport default sidebarService;", "map": {"version": 3, "names": ["SidebarService", "constructor", "baseUrl", "cache", "Map", "DEFAULT_TTL", "RELATED_ITEMS_TTL", "LATEST_ITEMS_TTL", "fetchSidebarData", "config", "related<PERSON>ey", "get<PERSON><PERSON><PERSON><PERSON>", "latestKey", "statsKey", "currentItem", "promises", "cachedRelated", "getFromCache", "push", "Promise", "resolve", "fetchRelatedItems", "then", "data", "setCache", "cachedLatest", "fetchLatestItems", "cachedStats", "fetchStatistics", "results", "all", "relatedItems", "latestItems", "statistics", "error", "console", "getFallbackData", "type", "parts", "excludeId", "limit", "join", "key", "cached", "get", "Date", "now", "timestamp", "ttl", "delete", "set", "_this$cache$get", "_this$cache$get2", "fallback<PERSON><PERSON><PERSON>", "fallback<PERSON><PERSON><PERSON>", "fetchWithRetry", "endpoint", "params", "URLSearchParams", "append", "toString", "getExcludeParam", "Error", "response", "fetchWithTimeout", "ok", "status", "statusText", "json", "url", "timeout", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "fetch", "signal", "headers", "clearTimeout", "fetchFn", "operation", "maxRetries", "lastError", "attempt", "warn", "delay", "Math", "pow", "encodeURIComponent", "clearCache", "startsWith", "clear", "getCacheStats", "size", "keys", "Array", "from", "preloadData", "configs", "map", "catch", "allSettled", "getCountryFlag", "country", "flagMap", "getLevelIcon", "level", "iconMap", "getOpportunityIcon", "sidebarService"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/sidebarService.ts"], "sourcesContent": ["/**\n * Professional Sidebar Data Service\n * Unified service for fetching sidebar data across all dedicated pages\n * Following industry standards like greatyop.com\n */\n\nexport interface SidebarData {\n  relatedItems: any[];\n  latestItems: any[];\n  statistics?: any;\n}\n\nexport interface SidebarConfig {\n  type: 'countries' | 'levels' | 'opportunities';\n  currentItem?: string;\n  excludeId?: number;\n  limit?: number;\n}\n\nclass SidebarService {\n  private baseUrl = '/api';\n  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();\n  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes\n  private readonly RELATED_ITEMS_TTL = 10 * 60 * 1000; // 10 minutes (changes less frequently)\n  private readonly LATEST_ITEMS_TTL = 2 * 60 * 1000; // 2 minutes (changes more frequently)\n\n  /**\n   * Fetch unified sidebar data with intelligent caching and parallel requests\n   */\n  async fetchSidebarData(config: SidebarConfig): Promise<SidebarData> {\n    try {\n      // Create cache keys for different data types\n      const relatedKey = this.getCacheKey('related', config);\n      const latestKey = this.getCacheKey('latest', config);\n      const statsKey = config.currentItem ? this.getCacheKey('stats', config) : null;\n\n      // Check cache first, then fetch missing data in parallel\n      const promises = [];\n\n      // Related items (cached longer as they change less frequently)\n      const cachedRelated = this.getFromCache(relatedKey);\n      if (cachedRelated) {\n        promises.push(Promise.resolve(cachedRelated));\n      } else {\n        promises.push(this.fetchRelatedItems(config).then(data => {\n          this.setCache(relatedKey, data, this.RELATED_ITEMS_TTL);\n          return data;\n        }));\n      }\n\n      // Latest items (cached shorter as they change more frequently)\n      const cachedLatest = this.getFromCache(latestKey);\n      if (cachedLatest) {\n        promises.push(Promise.resolve(cachedLatest));\n      } else {\n        promises.push(this.fetchLatestItems(config).then(data => {\n          this.setCache(latestKey, data, this.LATEST_ITEMS_TTL);\n          return data;\n        }));\n      }\n\n      // Statistics (if needed)\n      if (statsKey) {\n        const cachedStats = this.getFromCache(statsKey);\n        if (cachedStats) {\n          promises.push(Promise.resolve(cachedStats));\n        } else {\n          promises.push(this.fetchStatistics(config).then(data => {\n            this.setCache(statsKey, data, this.DEFAULT_TTL);\n            return data;\n          }));\n        }\n      }\n\n      const results = await Promise.all(promises);\n      const [relatedItems, latestItems, statistics] = results;\n\n      return {\n        relatedItems: relatedItems || [],\n        latestItems: latestItems || [],\n        statistics: statistics || null\n      };\n    } catch (error) {\n      console.error('Error fetching sidebar data:', error);\n      // Return cached data if available, otherwise empty data\n      return this.getFallbackData(config);\n    }\n  }\n\n  /**\n   * Generate cache key for different data types\n   */\n  private getCacheKey(type: string, config: SidebarConfig): string {\n    const parts = [type, config.type];\n    if (config.currentItem) parts.push(config.currentItem);\n    if (config.excludeId) parts.push(`exclude-${config.excludeId}`);\n    if (config.limit) parts.push(`limit-${config.limit}`);\n    return parts.join(':');\n  }\n\n  /**\n   * Get data from cache if valid\n   */\n  private getFromCache(key: string): any | null {\n    const cached = this.cache.get(key);\n    if (cached && Date.now() - cached.timestamp < cached.ttl) {\n      return cached.data;\n    }\n    if (cached) {\n      this.cache.delete(key); // Remove expired cache\n    }\n    return null;\n  }\n\n  /**\n   * Set data in cache with TTL\n   */\n  private setCache(key: string, data: any, ttl: number): void {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl\n    });\n  }\n\n  /**\n   * Get fallback data from cache (even if expired) or return empty data\n   */\n  private getFallbackData(config: SidebarConfig): SidebarData {\n    const relatedKey = this.getCacheKey('related', config);\n    const latestKey = this.getCacheKey('latest', config);\n\n    const fallbackRelated = this.cache.get(relatedKey)?.data || [];\n    const fallbackLatest = this.cache.get(latestKey)?.data || [];\n\n    return {\n      relatedItems: fallbackRelated,\n      latestItems: fallbackLatest\n    };\n  }\n\n  /**\n   * Fetch related items with retry logic and better error handling\n   */\n  private async fetchRelatedItems(config: SidebarConfig): Promise<any[]> {\n    return this.fetchWithRetry(async () => {\n      let endpoint = '';\n      const params = new URLSearchParams();\n\n      if (config.limit) {\n        params.append('limit', config.limit.toString());\n      }\n\n      if (config.currentItem) {\n        params.append(`exclude${this.getExcludeParam(config.type)}`, config.currentItem);\n      }\n\n      switch (config.type) {\n        case 'countries':\n          endpoint = `${this.baseUrl}/countries/sidebar`;\n          break;\n        case 'levels':\n          endpoint = `${this.baseUrl}/scholarships/levels`;\n          break;\n        case 'opportunities':\n          endpoint = `${this.baseUrl}/opportunities/types-sidebar`;\n          break;\n        default:\n          throw new Error(`Unknown config type: ${config.type}`);\n      }\n\n      const response = await this.fetchWithTimeout(`${endpoint}?${params}`, 5000);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return data.data || [];\n    }, `related-${config.type}`);\n  }\n\n  /**\n   * Fetch latest items with enhanced error handling\n   */\n  private async fetchLatestItems(config: SidebarConfig): Promise<any[]> {\n    return this.fetchWithRetry(async () => {\n      const params = new URLSearchParams();\n      params.append('limit', '6');\n\n      if (config.excludeId) {\n        params.append('excludeId', config.excludeId.toString());\n      }\n\n      let endpoint = '';\n      if (config.type === 'opportunities') {\n        endpoint = `${this.baseUrl}/opportunities/latest`;\n      } else {\n        endpoint = `${this.baseUrl}/scholarships/latest`;\n      }\n\n      const response = await this.fetchWithTimeout(`${endpoint}?${params}`, 5000);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      return data.data || [];\n    }, `latest-${config.type}`);\n  }\n\n  /**\n   * Fetch with timeout to prevent hanging requests\n   */\n  private async fetchWithTimeout(url: string, timeout: number): Promise<Response> {\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), timeout);\n\n    try {\n      const response = await fetch(url, {\n        signal: controller.signal,\n        headers: {\n          'Content-Type': 'application/json',\n        }\n      });\n      clearTimeout(timeoutId);\n      return response;\n    } catch (error) {\n      clearTimeout(timeoutId);\n      throw error;\n    }\n  }\n\n  /**\n   * Retry logic for failed requests\n   */\n  private async fetchWithRetry<T>(\n    fetchFn: () => Promise<T>,\n    operation: string,\n    maxRetries: number = 2\n  ): Promise<T> {\n    let lastError: Error;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        return await fetchFn();\n      } catch (error) {\n        lastError = error as Error;\n        console.warn(`${operation} attempt ${attempt + 1} failed:`, error);\n\n        if (attempt < maxRetries) {\n          // Exponential backoff: 500ms, 1000ms, 2000ms\n          const delay = 500 * Math.pow(2, attempt);\n          await new Promise(resolve => setTimeout(resolve, delay));\n        }\n      }\n    }\n\n    console.error(`${operation} failed after ${maxRetries + 1} attempts:`, lastError);\n    throw lastError;\n  }\n\n  /**\n   * Fetch statistics with enhanced error handling\n   */\n  private async fetchStatistics(config: SidebarConfig): Promise<any> {\n    if (!config.currentItem) return null;\n\n    return this.fetchWithRetry(async () => {\n      let endpoint = '';\n\n      switch (config.type) {\n        case 'countries':\n          endpoint = `${this.baseUrl}/countries/${encodeURIComponent(config.currentItem)}/statistics`;\n          break;\n        case 'levels':\n          // Level statistics can be computed from the main data\n          return null;\n        case 'opportunities':\n          // Opportunity type statistics can be computed from the main data\n          return null;\n      }\n\n      if (endpoint) {\n        const response = await this.fetchWithTimeout(endpoint, 5000);\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const data = await response.json();\n        return data.data;\n      }\n      return null;\n    }, `stats-${config.type}-${config.currentItem}`);\n  }\n\n  /**\n   * Clear cache for specific type or all cache\n   */\n  public clearCache(type?: string): void {\n    if (type) {\n      // Clear cache entries that start with the type\n      for (const [key] of this.cache) {\n        if (key.startsWith(type)) {\n          this.cache.delete(key);\n        }\n      }\n    } else {\n      // Clear all cache\n      this.cache.clear();\n    }\n  }\n\n  /**\n   * Get cache statistics for debugging\n   */\n  public getCacheStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n\n  /**\n   * Preload data for better performance\n   */\n  public async preloadData(configs: SidebarConfig[]): Promise<void> {\n    const promises = configs.map(config =>\n      this.fetchSidebarData(config).catch(error => {\n        console.warn('Preload failed for config:', config, error);\n      })\n    );\n\n    await Promise.allSettled(promises);\n  }\n\n  /**\n   * Get the correct exclude parameter name based on type\n   */\n  private getExcludeParam(type: string): string {\n    switch (type) {\n      case 'countries':\n        return 'Country';\n      case 'levels':\n        return 'Level';\n      case 'opportunities':\n        return 'Type';\n      default:\n        return 'Item';\n    }\n  }\n\n  /**\n   * Get country flag emoji (utility function)\n   */\n  getCountryFlag(country: string): string {\n    const flagMap: { [key: string]: string } = {\n      'France': '🇫🇷',\n      'Germany': '🇩🇪',\n      'United Kingdom': '🇬🇧',\n      'United States': '🇺🇸',\n      'Canada': '🇨🇦',\n      'Australia': '🇦🇺',\n      'Netherlands': '🇳🇱',\n      'Sweden': '🇸🇪',\n      'Norway': '🇳🇴',\n      'Denmark': '🇩🇰',\n      'Switzerland': '🇨🇭',\n      'Belgium': '🇧🇪',\n      'Austria': '🇦🇹',\n      'Italy': '🇮🇹',\n      'Spain': '🇪🇸',\n      'Japan': '🇯🇵',\n      'South Korea': '🇰🇷',\n      'Singapore': '🇸🇬',\n      'New Zealand': '🇳🇿',\n      'Finland': '🇫🇮'\n    };\n    return flagMap[country] || '🌍';\n  }\n\n  /**\n   * Get level icon (utility function)\n   */\n  getLevelIcon(level: string): string {\n    const iconMap: { [key: string]: string } = {\n      'Bachelor': '🎓',\n      'Master': '📚',\n      'PhD': '🔬',\n      'Postdoc': '👨‍🔬',\n      'Undergraduate': '🎓',\n      'Graduate': '📚',\n      'Doctorate': '🔬'\n    };\n    return iconMap[level] || '📖';\n  }\n\n  /**\n   * Get opportunity type icon (utility function)\n   */\n  getOpportunityIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'internship': '💼',\n      'training': '🎯',\n      'conference': '🎤',\n      'workshop': '🛠️',\n      'competition': '🏆'\n    };\n    return iconMap[type] || '🔗';\n  }\n}\n\nexport const sidebarService = new SidebarService();\nexport default sidebarService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAeA,MAAMA,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,OAAO,GAAG,MAAM;IAAA,KAChBC,KAAK,GAAG,IAAIC,GAAG,CAAwD,CAAC;IAAA,KAC/DC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAAA,KAC7BC,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAAA,KACpCC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;EAAA;EAAE;;EAEnD;AACF;AACA;EACE,MAAMC,gBAAgBA,CAACC,MAAqB,EAAwB;IAClE,IAAI;MACF;MACA,MAAMC,UAAU,GAAG,IAAI,CAACC,WAAW,CAAC,SAAS,EAAEF,MAAM,CAAC;MACtD,MAAMG,SAAS,GAAG,IAAI,CAACD,WAAW,CAAC,QAAQ,EAAEF,MAAM,CAAC;MACpD,MAAMI,QAAQ,GAAGJ,MAAM,CAACK,WAAW,GAAG,IAAI,CAACH,WAAW,CAAC,OAAO,EAAEF,MAAM,CAAC,GAAG,IAAI;;MAE9E;MACA,MAAMM,QAAQ,GAAG,EAAE;;MAEnB;MACA,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,CAACP,UAAU,CAAC;MACnD,IAAIM,aAAa,EAAE;QACjBD,QAAQ,CAACG,IAAI,CAACC,OAAO,CAACC,OAAO,CAACJ,aAAa,CAAC,CAAC;MAC/C,CAAC,MAAM;QACLD,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACG,iBAAiB,CAACZ,MAAM,CAAC,CAACa,IAAI,CAACC,IAAI,IAAI;UACxD,IAAI,CAACC,QAAQ,CAACd,UAAU,EAAEa,IAAI,EAAE,IAAI,CAACjB,iBAAiB,CAAC;UACvD,OAAOiB,IAAI;QACb,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,MAAME,YAAY,GAAG,IAAI,CAACR,YAAY,CAACL,SAAS,CAAC;MACjD,IAAIa,YAAY,EAAE;QAChBV,QAAQ,CAACG,IAAI,CAACC,OAAO,CAACC,OAAO,CAACK,YAAY,CAAC,CAAC;MAC9C,CAAC,MAAM;QACLV,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACQ,gBAAgB,CAACjB,MAAM,CAAC,CAACa,IAAI,CAACC,IAAI,IAAI;UACvD,IAAI,CAACC,QAAQ,CAACZ,SAAS,EAAEW,IAAI,EAAE,IAAI,CAAChB,gBAAgB,CAAC;UACrD,OAAOgB,IAAI;QACb,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,IAAIV,QAAQ,EAAE;QACZ,MAAMc,WAAW,GAAG,IAAI,CAACV,YAAY,CAACJ,QAAQ,CAAC;QAC/C,IAAIc,WAAW,EAAE;UACfZ,QAAQ,CAACG,IAAI,CAACC,OAAO,CAACC,OAAO,CAACO,WAAW,CAAC,CAAC;QAC7C,CAAC,MAAM;UACLZ,QAAQ,CAACG,IAAI,CAAC,IAAI,CAACU,eAAe,CAACnB,MAAM,CAAC,CAACa,IAAI,CAACC,IAAI,IAAI;YACtD,IAAI,CAACC,QAAQ,CAACX,QAAQ,EAAEU,IAAI,EAAE,IAAI,CAAClB,WAAW,CAAC;YAC/C,OAAOkB,IAAI;UACb,CAAC,CAAC,CAAC;QACL;MACF;MAEA,MAAMM,OAAO,GAAG,MAAMV,OAAO,CAACW,GAAG,CAACf,QAAQ,CAAC;MAC3C,MAAM,CAACgB,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAGJ,OAAO;MAEvD,OAAO;QACLE,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCC,WAAW,EAAEA,WAAW,IAAI,EAAE;QAC9BC,UAAU,EAAEA,UAAU,IAAI;MAC5B,CAAC;IACH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACA,OAAO,IAAI,CAACE,eAAe,CAAC3B,MAAM,CAAC;IACrC;EACF;;EAEA;AACF;AACA;EACUE,WAAWA,CAAC0B,IAAY,EAAE5B,MAAqB,EAAU;IAC/D,MAAM6B,KAAK,GAAG,CAACD,IAAI,EAAE5B,MAAM,CAAC4B,IAAI,CAAC;IACjC,IAAI5B,MAAM,CAACK,WAAW,EAAEwB,KAAK,CAACpB,IAAI,CAACT,MAAM,CAACK,WAAW,CAAC;IACtD,IAAIL,MAAM,CAAC8B,SAAS,EAAED,KAAK,CAACpB,IAAI,CAAC,WAAWT,MAAM,CAAC8B,SAAS,EAAE,CAAC;IAC/D,IAAI9B,MAAM,CAAC+B,KAAK,EAAEF,KAAK,CAACpB,IAAI,CAAC,SAAST,MAAM,CAAC+B,KAAK,EAAE,CAAC;IACrD,OAAOF,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC;EACxB;;EAEA;AACF;AACA;EACUxB,YAAYA,CAACyB,GAAW,EAAc;IAC5C,MAAMC,MAAM,GAAG,IAAI,CAACxC,KAAK,CAACyC,GAAG,CAACF,GAAG,CAAC;IAClC,IAAIC,MAAM,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACI,SAAS,GAAGJ,MAAM,CAACK,GAAG,EAAE;MACxD,OAAOL,MAAM,CAACpB,IAAI;IACpB;IACA,IAAIoB,MAAM,EAAE;MACV,IAAI,CAACxC,KAAK,CAAC8C,MAAM,CAACP,GAAG,CAAC,CAAC,CAAC;IAC1B;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACUlB,QAAQA,CAACkB,GAAW,EAAEnB,IAAS,EAAEyB,GAAW,EAAQ;IAC1D,IAAI,CAAC7C,KAAK,CAAC+C,GAAG,CAACR,GAAG,EAAE;MAClBnB,IAAI;MACJwB,SAAS,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBE;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACUZ,eAAeA,CAAC3B,MAAqB,EAAe;IAAA,IAAA0C,eAAA,EAAAC,gBAAA;IAC1D,MAAM1C,UAAU,GAAG,IAAI,CAACC,WAAW,CAAC,SAAS,EAAEF,MAAM,CAAC;IACtD,MAAMG,SAAS,GAAG,IAAI,CAACD,WAAW,CAAC,QAAQ,EAAEF,MAAM,CAAC;IAEpD,MAAM4C,eAAe,GAAG,EAAAF,eAAA,OAAI,CAAChD,KAAK,CAACyC,GAAG,CAAClC,UAAU,CAAC,cAAAyC,eAAA,uBAA1BA,eAAA,CAA4B5B,IAAI,KAAI,EAAE;IAC9D,MAAM+B,cAAc,GAAG,EAAAF,gBAAA,OAAI,CAACjD,KAAK,CAACyC,GAAG,CAAChC,SAAS,CAAC,cAAAwC,gBAAA,uBAAzBA,gBAAA,CAA2B7B,IAAI,KAAI,EAAE;IAE5D,OAAO;MACLQ,YAAY,EAAEsB,eAAe;MAC7BrB,WAAW,EAAEsB;IACf,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAcjC,iBAAiBA,CAACZ,MAAqB,EAAkB;IACrE,OAAO,IAAI,CAAC8C,cAAc,CAAC,YAAY;MACrC,IAAIC,QAAQ,GAAG,EAAE;MACjB,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MAEpC,IAAIjD,MAAM,CAAC+B,KAAK,EAAE;QAChBiB,MAAM,CAACE,MAAM,CAAC,OAAO,EAAElD,MAAM,CAAC+B,KAAK,CAACoB,QAAQ,CAAC,CAAC,CAAC;MACjD;MAEA,IAAInD,MAAM,CAACK,WAAW,EAAE;QACtB2C,MAAM,CAACE,MAAM,CAAC,UAAU,IAAI,CAACE,eAAe,CAACpD,MAAM,CAAC4B,IAAI,CAAC,EAAE,EAAE5B,MAAM,CAACK,WAAW,CAAC;MAClF;MAEA,QAAQL,MAAM,CAAC4B,IAAI;QACjB,KAAK,WAAW;UACdmB,QAAQ,GAAG,GAAG,IAAI,CAACtD,OAAO,oBAAoB;UAC9C;QACF,KAAK,QAAQ;UACXsD,QAAQ,GAAG,GAAG,IAAI,CAACtD,OAAO,sBAAsB;UAChD;QACF,KAAK,eAAe;UAClBsD,QAAQ,GAAG,GAAG,IAAI,CAACtD,OAAO,8BAA8B;UACxD;QACF;UACE,MAAM,IAAI4D,KAAK,CAAC,wBAAwBrD,MAAM,CAAC4B,IAAI,EAAE,CAAC;MAC1D;MAEA,MAAM0B,QAAQ,GAAG,MAAM,IAAI,CAACC,gBAAgB,CAAC,GAAGR,QAAQ,IAAIC,MAAM,EAAE,EAAE,IAAI,CAAC;MAC3E,IAAI,CAACM,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIH,KAAK,CAAC,QAAQC,QAAQ,CAACG,MAAM,KAAKH,QAAQ,CAACI,UAAU,EAAE,CAAC;MACpE;MAEA,MAAM5C,IAAI,GAAG,MAAMwC,QAAQ,CAACK,IAAI,CAAC,CAAC;MAClC,OAAO7C,IAAI,CAACA,IAAI,IAAI,EAAE;IACxB,CAAC,EAAE,WAAWd,MAAM,CAAC4B,IAAI,EAAE,CAAC;EAC9B;;EAEA;AACF;AACA;EACE,MAAcX,gBAAgBA,CAACjB,MAAqB,EAAkB;IACpE,OAAO,IAAI,CAAC8C,cAAc,CAAC,YAAY;MACrC,MAAME,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpCD,MAAM,CAACE,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC;MAE3B,IAAIlD,MAAM,CAAC8B,SAAS,EAAE;QACpBkB,MAAM,CAACE,MAAM,CAAC,WAAW,EAAElD,MAAM,CAAC8B,SAAS,CAACqB,QAAQ,CAAC,CAAC,CAAC;MACzD;MAEA,IAAIJ,QAAQ,GAAG,EAAE;MACjB,IAAI/C,MAAM,CAAC4B,IAAI,KAAK,eAAe,EAAE;QACnCmB,QAAQ,GAAG,GAAG,IAAI,CAACtD,OAAO,uBAAuB;MACnD,CAAC,MAAM;QACLsD,QAAQ,GAAG,GAAG,IAAI,CAACtD,OAAO,sBAAsB;MAClD;MAEA,MAAM6D,QAAQ,GAAG,MAAM,IAAI,CAACC,gBAAgB,CAAC,GAAGR,QAAQ,IAAIC,MAAM,EAAE,EAAE,IAAI,CAAC;MAC3E,IAAI,CAACM,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIH,KAAK,CAAC,QAAQC,QAAQ,CAACG,MAAM,KAAKH,QAAQ,CAACI,UAAU,EAAE,CAAC;MACpE;MAEA,MAAM5C,IAAI,GAAG,MAAMwC,QAAQ,CAACK,IAAI,CAAC,CAAC;MAClC,OAAO7C,IAAI,CAACA,IAAI,IAAI,EAAE;IACxB,CAAC,EAAE,UAAUd,MAAM,CAAC4B,IAAI,EAAE,CAAC;EAC7B;;EAEA;AACF;AACA;EACE,MAAc2B,gBAAgBA,CAACK,GAAW,EAAEC,OAAe,EAAqB;IAC9E,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAEL,OAAO,CAAC;IAE/D,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMa,KAAK,CAACP,GAAG,EAAE;QAChCQ,MAAM,EAAEN,UAAU,CAACM,MAAM;QACzBC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACFC,YAAY,CAACN,SAAS,CAAC;MACvB,OAAOV,QAAQ;IACjB,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACd6C,YAAY,CAACN,SAAS,CAAC;MACvB,MAAMvC,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAcqB,cAAcA,CAC1ByB,OAAyB,EACzBC,SAAiB,EACjBC,UAAkB,GAAG,CAAC,EACV;IACZ,IAAIC,SAAgB;IAEpB,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAIF,UAAU,EAAEE,OAAO,EAAE,EAAE;MACtD,IAAI;QACF,OAAO,MAAMJ,OAAO,CAAC,CAAC;MACxB,CAAC,CAAC,OAAO9C,KAAK,EAAE;QACdiD,SAAS,GAAGjD,KAAc;QAC1BC,OAAO,CAACkD,IAAI,CAAC,GAAGJ,SAAS,YAAYG,OAAO,GAAG,CAAC,UAAU,EAAElD,KAAK,CAAC;QAElE,IAAIkD,OAAO,GAAGF,UAAU,EAAE;UACxB;UACA,MAAMI,KAAK,GAAG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,OAAO,CAAC;UACxC,MAAM,IAAIjE,OAAO,CAACC,OAAO,IAAIsD,UAAU,CAACtD,OAAO,EAAEkE,KAAK,CAAC,CAAC;QAC1D;MACF;IACF;IAEAnD,OAAO,CAACD,KAAK,CAAC,GAAG+C,SAAS,iBAAiBC,UAAU,GAAG,CAAC,YAAY,EAAEC,SAAS,CAAC;IACjF,MAAMA,SAAS;EACjB;;EAEA;AACF;AACA;EACE,MAAcvD,eAAeA,CAACnB,MAAqB,EAAgB;IACjE,IAAI,CAACA,MAAM,CAACK,WAAW,EAAE,OAAO,IAAI;IAEpC,OAAO,IAAI,CAACyC,cAAc,CAAC,YAAY;MACrC,IAAIC,QAAQ,GAAG,EAAE;MAEjB,QAAQ/C,MAAM,CAAC4B,IAAI;QACjB,KAAK,WAAW;UACdmB,QAAQ,GAAG,GAAG,IAAI,CAACtD,OAAO,cAAcuF,kBAAkB,CAAChF,MAAM,CAACK,WAAW,CAAC,aAAa;UAC3F;QACF,KAAK,QAAQ;UACX;UACA,OAAO,IAAI;QACb,KAAK,eAAe;UAClB;UACA,OAAO,IAAI;MACf;MAEA,IAAI0C,QAAQ,EAAE;QACZ,MAAMO,QAAQ,GAAG,MAAM,IAAI,CAACC,gBAAgB,CAACR,QAAQ,EAAE,IAAI,CAAC;QAC5D,IAAI,CAACO,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIH,KAAK,CAAC,QAAQC,QAAQ,CAACG,MAAM,KAAKH,QAAQ,CAACI,UAAU,EAAE,CAAC;QACpE;QACA,MAAM5C,IAAI,GAAG,MAAMwC,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClC,OAAO7C,IAAI,CAACA,IAAI;MAClB;MACA,OAAO,IAAI;IACb,CAAC,EAAE,SAASd,MAAM,CAAC4B,IAAI,IAAI5B,MAAM,CAACK,WAAW,EAAE,CAAC;EAClD;;EAEA;AACF;AACA;EACS4E,UAAUA,CAACrD,IAAa,EAAQ;IACrC,IAAIA,IAAI,EAAE;MACR;MACA,KAAK,MAAM,CAACK,GAAG,CAAC,IAAI,IAAI,CAACvC,KAAK,EAAE;QAC9B,IAAIuC,GAAG,CAACiD,UAAU,CAACtD,IAAI,CAAC,EAAE;UACxB,IAAI,CAAClC,KAAK,CAAC8C,MAAM,CAACP,GAAG,CAAC;QACxB;MACF;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACvC,KAAK,CAACyF,KAAK,CAAC,CAAC;IACpB;EACF;;EAEA;AACF;AACA;EACSC,aAAaA,CAAA,EAAqC;IACvD,OAAO;MACLC,IAAI,EAAE,IAAI,CAAC3F,KAAK,CAAC2F,IAAI;MACrBC,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC9F,KAAK,CAAC4F,IAAI,CAAC,CAAC;IACpC,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAaG,WAAWA,CAACC,OAAwB,EAAiB;IAChE,MAAMpF,QAAQ,GAAGoF,OAAO,CAACC,GAAG,CAAC3F,MAAM,IACjC,IAAI,CAACD,gBAAgB,CAACC,MAAM,CAAC,CAAC4F,KAAK,CAACnE,KAAK,IAAI;MAC3CC,OAAO,CAACkD,IAAI,CAAC,4BAA4B,EAAE5E,MAAM,EAAEyB,KAAK,CAAC;IAC3D,CAAC,CACH,CAAC;IAED,MAAMf,OAAO,CAACmF,UAAU,CAACvF,QAAQ,CAAC;EACpC;;EAEA;AACF;AACA;EACU8C,eAAeA,CAACxB,IAAY,EAAU;IAC5C,QAAQA,IAAI;MACV,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,eAAe;QAClB,OAAO,MAAM;MACf;QACE,OAAO,MAAM;IACjB;EACF;;EAEA;AACF;AACA;EACEkE,cAAcA,CAACC,OAAe,EAAU;IACtC,MAAMC,OAAkC,GAAG;MACzC,QAAQ,EAAE,MAAM;MAChB,SAAS,EAAE,MAAM;MACjB,gBAAgB,EAAE,MAAM;MACxB,eAAe,EAAE,MAAM;MACvB,QAAQ,EAAE,MAAM;MAChB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,MAAM;MACrB,QAAQ,EAAE,MAAM;MAChB,QAAQ,EAAE,MAAM;MAChB,SAAS,EAAE,MAAM;MACjB,aAAa,EAAE,MAAM;MACrB,SAAS,EAAE,MAAM;MACjB,SAAS,EAAE,MAAM;MACjB,OAAO,EAAE,MAAM;MACf,OAAO,EAAE,MAAM;MACf,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,MAAM;MACrB,WAAW,EAAE,MAAM;MACnB,aAAa,EAAE,MAAM;MACrB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,OAAO,CAACD,OAAO,CAAC,IAAI,IAAI;EACjC;;EAEA;AACF;AACA;EACEE,YAAYA,CAACC,KAAa,EAAU;IAClC,MAAMC,OAAkC,GAAG;MACzC,UAAU,EAAE,IAAI;MAChB,QAAQ,EAAE,IAAI;MACd,KAAK,EAAE,IAAI;MACX,SAAS,EAAE,OAAO;MAClB,eAAe,EAAE,IAAI;MACrB,UAAU,EAAE,IAAI;MAChB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,OAAO,CAACD,KAAK,CAAC,IAAI,IAAI;EAC/B;;EAEA;AACF;AACA;EACEE,kBAAkBA,CAACxE,IAAY,EAAU;IACvC,MAAMuE,OAAkC,GAAG;MACzC,YAAY,EAAE,IAAI;MAClB,UAAU,EAAE,IAAI;MAChB,YAAY,EAAE,IAAI;MAClB,UAAU,EAAE,KAAK;MACjB,aAAa,EAAE;IACjB,CAAC;IACD,OAAOA,OAAO,CAACvE,IAAI,CAAC,IAAI,IAAI;EAC9B;AACF;AAEA,OAAO,MAAMyE,cAAc,GAAG,IAAI9G,cAAc,CAAC,CAAC;AAClD,eAAe8G,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}