{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx\",\n  _s = $RefreshSig$();\n/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState } from 'react';\nimport { ChevronDown } from 'lucide-react';\nimport Dropdown from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavigationDropdown = ({\n  type,\n  label,\n  className = ''\n}) => {\n  _s();\n  const [items, setItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    translations\n  } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n\n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor = () => [];\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = countries => [{\n            id: 'all-countries',\n            label: translations.navigation.allCountries || 'All Countries',\n            href: '/countries',\n            count: countries.reduce((sum, c) => sum + c.count, 0)\n          }, ...countries.slice(0, 8).map(country => ({\n            id: country.slug,\n            label: country.name,\n            href: `/countries/${encodeURIComponent(country.name)}`,\n            count: country.count\n          })), ...(countries.length > 8 ? [{\n            id: 'view-all-countries',\n            label: translations.navigation.viewAll || 'View All',\n            href: '/countries'\n          }] : [])];\n          break;\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = levels => [{\n            id: 'all-scholarships',\n            label: translations.navigation.allScholarships || 'All Scholarships',\n            href: '/scholarships',\n            icon: /*#__PURE__*/_jsxDEV(GraduationCap, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 21\n            }, this),\n            count: levels.reduce((sum, l) => sum + l.count, 0)\n          }, ...levels.map(level => ({\n            id: level.slug,\n            label: level.name,\n            href: `/scholarships?level=${encodeURIComponent(level.name)}`,\n            count: level.openCount\n          }))];\n          break;\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = types => [{\n            id: 'all-opportunities',\n            label: translations.navigation.allOpportunities || 'All Opportunities',\n            href: '/opportunities',\n            icon: /*#__PURE__*/_jsxDEV(Briefcase, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 21\n            }, this),\n            count: types.reduce((sum, t) => sum + t.count, 0)\n          }, ...types.map(opType => ({\n            id: opType.slug,\n            label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n            href: `/opportunities?type=${encodeURIComponent(opType.name)}`,\n            count: opType.activeCount\n          }))];\n          break;\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      const result = await response.json();\n      const data = result.data || result;\n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getIcon = () => {\n    switch (type) {\n      case 'countries':\n        return /*#__PURE__*/_jsxDEV(MapPin, {\n          size: 16,\n          className: \"text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 16\n        }, this);\n      case 'scholarships':\n        return /*#__PURE__*/_jsxDEV(GraduationCap, {\n          size: 16,\n          className: \"text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 16\n        }, this);\n      case 'opportunities':\n        return /*#__PURE__*/_jsxDEV(Briefcase, {\n          size: 16,\n          className: \"text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const trigger = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n      transition-all duration-300 ease-in-out\n      text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm\n      group-hover:scale-105 transform\n      ${className}\n    `,\n    children: [getIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"transition-all duration-200\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n      size: 16,\n      className: \"text-gray-400 transition-all duration-300 ease-out group-hover:rotate-180 group-hover:text-primary\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dropdown, {\n    trigger: trigger,\n    items: items,\n    loading: loading,\n    onOpen: fetchData,\n    showOnHover: true,\n    closeOnClick: true,\n    placement: \"bottom-left\",\n    className: \"group\",\n    dropdownClassName: \"border-t-2 border-primary\",\n    emptyMessage: `No ${type} available`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this);\n};\n_s(NavigationDropdown, \"dSYAOEQJpmUEaAadZBeAZJtT6G0=\", false, function () {\n  return [useLanguage];\n});\n_c = NavigationDropdown;\nexport default NavigationDropdown;\nvar _c;\n$RefreshReg$(_c, \"NavigationDropdown\");", "map": {"version": 3, "names": ["React", "useState", "ChevronDown", "Dropdown", "useLanguage", "jsxDEV", "_jsxDEV", "NavigationDropdown", "type", "label", "className", "_s", "items", "setItems", "loading", "setLoading", "translations", "fetchData", "length", "endpoint", "dataProcessor", "countries", "id", "navigation", "allCountries", "href", "count", "reduce", "sum", "c", "slice", "map", "country", "slug", "name", "encodeURIComponent", "viewAll", "levels", "allScholarships", "icon", "GraduationCap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "l", "level", "openCount", "types", "allOpportunities", "Briefcase", "t", "opType", "char<PERSON>t", "toUpperCase", "activeCount", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "result", "json", "data", "error", "console", "disabled", "getIcon", "MapPin", "trigger", "children", "onOpen", "showOnHover", "closeOnClick", "placement", "dropdownClassName", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx"], "sourcesContent": ["/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport Dropdown, { DropdownItem } from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\n\ninterface NavigationDropdownProps {\n  type: 'countries' | 'scholarships' | 'opportunities';\n  label: string;\n  className?: string;\n}\n\ninterface CountryData {\n  name: string;\n  count: number;\n  slug: string;\n}\n\ninterface LevelData {\n  name: string;\n  count: number;\n  openCount: number;\n  slug: string;\n}\n\ninterface OpportunityTypeData {\n  name: string;\n  count: number;\n  activeCount: number;\n  slug: string;\n}\n\nconst NavigationDropdown: React.FC<NavigationDropdownProps> = ({\n  type,\n  label,\n  className = ''\n}) => {\n  const [items, setItems] = useState<DropdownItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const { translations } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n    \n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor: (data: any[]) => DropdownItem[] = () => [];\n\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = (countries: CountryData[]) => [\n            {\n              id: 'all-countries',\n              label: translations.navigation.allCountries || 'All Countries',\n              href: '/countries',\n              count: countries.reduce((sum, c) => sum + c.count, 0)\n            },\n            ...countries.slice(0, 8).map(country => ({\n              id: country.slug,\n              label: country.name,\n              href: `/countries/${encodeURIComponent(country.name)}`,\n              count: country.count\n            })),\n            ...(countries.length > 8 ? [{\n              id: 'view-all-countries',\n              label: translations.navigation.viewAll || 'View All',\n              href: '/countries'\n            }] : [])\n          ];\n          break;\n\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = (levels: LevelData[]) => [\n            {\n              id: 'all-scholarships',\n              label: translations.navigation.allScholarships || 'All Scholarships',\n              href: '/scholarships',\n              icon: <GraduationCap size={16} />,\n              count: levels.reduce((sum, l) => sum + l.count, 0)\n            },\n            ...levels.map(level => ({\n              id: level.slug,\n              label: level.name,\n              href: `/scholarships?level=${encodeURIComponent(level.name)}`,\n              count: level.openCount\n            }))\n          ];\n          break;\n\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = (types: OpportunityTypeData[]) => [\n            {\n              id: 'all-opportunities',\n              label: translations.navigation.allOpportunities || 'All Opportunities',\n              href: '/opportunities',\n              icon: <Briefcase size={16} />,\n              count: types.reduce((sum, t) => sum + t.count, 0)\n            },\n            ...types.map(opType => ({\n              id: opType.slug,\n              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n              href: `/opportunities?type=${encodeURIComponent(opType.name)}`,\n              count: opType.activeCount\n            }))\n          ];\n          break;\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      \n      const result = await response.json();\n      const data = result.data || result;\n      \n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getIcon = () => {\n    switch (type) {\n      case 'countries':\n        return <MapPin size={16} className=\"text-gray-500\" />;\n      case 'scholarships':\n        return <GraduationCap size={16} className=\"text-gray-500\" />;\n      case 'opportunities':\n        return <Briefcase size={16} className=\"text-gray-500\" />;\n      default:\n        return null;\n    }\n  };\n\n  const trigger = (\n    <div className={`\n      flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n      transition-all duration-300 ease-in-out\n      text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm\n      group-hover:scale-105 transform\n      ${className}\n    `}>\n      {getIcon()}\n      <span className=\"transition-all duration-200\">{label}</span>\n      <ChevronDown\n        size={16}\n        className=\"text-gray-400 transition-all duration-300 ease-out group-hover:rotate-180 group-hover:text-primary\"\n      />\n    </div>\n  );\n\n  return (\n    <Dropdown\n      trigger={trigger}\n      items={items}\n      loading={loading}\n      onOpen={fetchData}\n      showOnHover={true}\n      closeOnClick={true}\n      placement=\"bottom-left\"\n      className=\"group\"\n      dropdownClassName=\"border-t-2 border-primary\"\n      emptyMessage={`No ${type} available`}\n    />\n  );\n};\n\nexport default NavigationDropdown;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAElD,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,QAAQ,MAAwB,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4B5D,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,IAAI;EACJC,KAAK;EACLC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAiB,EAAE,CAAC;EACtD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEe;EAAa,CAAC,GAAGZ,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE9BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAII,QAAQ,GAAG,EAAE;MACjB,IAAIC,aAA8C,GAAGA,CAAA,KAAM,EAAE;MAE7D,QAAQZ,IAAI;QACV,KAAK,WAAW;UACdW,QAAQ,GAAG,gBAAgB;UAC3BC,aAAa,GAAIC,SAAwB,IAAK,CAC5C;YACEC,EAAE,EAAE,eAAe;YACnBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACC,YAAY,IAAI,eAAe;YAC9DC,IAAI,EAAE,YAAY;YAClBC,KAAK,EAAEL,SAAS,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACH,KAAK,EAAE,CAAC;UACtD,CAAC,EACD,GAAGL,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,OAAO,KAAK;YACvCV,EAAE,EAAEU,OAAO,CAACC,IAAI;YAChBxB,KAAK,EAAEuB,OAAO,CAACE,IAAI;YACnBT,IAAI,EAAE,cAAcU,kBAAkB,CAACH,OAAO,CAACE,IAAI,CAAC,EAAE;YACtDR,KAAK,EAAEM,OAAO,CAACN;UACjB,CAAC,CAAC,CAAC,EACH,IAAIL,SAAS,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;YAC1BI,EAAE,EAAE,oBAAoB;YACxBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACa,OAAO,IAAI,UAAU;YACpDX,IAAI,EAAE;UACR,CAAC,CAAC,GAAG,EAAE,CAAC,CACT;UACD;QAEF,KAAK,cAAc;UACjBN,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIiB,MAAmB,IAAK,CACvC;YACEf,EAAE,EAAE,kBAAkB;YACtBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACe,eAAe,IAAI,kBAAkB;YACpEb,IAAI,EAAE,eAAe;YACrBc,IAAI,eAAEjC,OAAA,CAACkC,aAAa;cAACC,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YACjCnB,KAAK,EAAEW,MAAM,CAACV,MAAM,CAAC,CAACC,GAAG,EAAEkB,CAAC,KAAKlB,GAAG,GAAGkB,CAAC,CAACpB,KAAK,EAAE,CAAC;UACnD,CAAC,EACD,GAAGW,MAAM,CAACN,GAAG,CAACgB,KAAK,KAAK;YACtBzB,EAAE,EAAEyB,KAAK,CAACd,IAAI;YACdxB,KAAK,EAAEsC,KAAK,CAACb,IAAI;YACjBT,IAAI,EAAE,uBAAuBU,kBAAkB,CAACY,KAAK,CAACb,IAAI,CAAC,EAAE;YAC7DR,KAAK,EAAEqB,KAAK,CAACC;UACf,CAAC,CAAC,CAAC,CACJ;UACD;QAEF,KAAK,eAAe;UAClB7B,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAI6B,KAA4B,IAAK,CAChD;YACE3B,EAAE,EAAE,mBAAmB;YACvBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAAC2B,gBAAgB,IAAI,mBAAmB;YACtEzB,IAAI,EAAE,gBAAgB;YACtBc,IAAI,eAAEjC,OAAA,CAAC6C,SAAS;cAACV,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YAC7BnB,KAAK,EAAEuB,KAAK,CAACtB,MAAM,CAAC,CAACC,GAAG,EAAEwB,CAAC,KAAKxB,GAAG,GAAGwB,CAAC,CAAC1B,KAAK,EAAE,CAAC;UAClD,CAAC,EACD,GAAGuB,KAAK,CAAClB,GAAG,CAACsB,MAAM,KAAK;YACtB/B,EAAE,EAAE+B,MAAM,CAACpB,IAAI;YACfxB,KAAK,EAAE4C,MAAM,CAACnB,IAAI,CAACoB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACnB,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC;YACjEL,IAAI,EAAE,uBAAuBU,kBAAkB,CAACkB,MAAM,CAACnB,IAAI,CAAC,EAAE;YAC9DR,KAAK,EAAE2B,MAAM,CAACG;UAChB,CAAC,CAAC,CAAC,CACJ;UACD;MACJ;MAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAG1C,QAAQ,EAAE,CAAC;MACtG,IAAI,CAACsC,QAAQ,CAACK,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MAEzD,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM;MAElCnD,QAAQ,CAACO,aAAa,CAAC8C,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB3D,IAAI,QAAQ,EAAE2D,KAAK,CAAC;MACpDtD,QAAQ,CAAC,CAAC;QACRS,EAAE,EAAE,OAAO;QACXb,KAAK,EAAE,qBAAqB;QAC5B4D,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRtD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuD,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQ9D,IAAI;MACV,KAAK,WAAW;QACd,oBAAOF,OAAA,CAACiE,MAAM;UAAC9B,IAAI,EAAE,EAAG;UAAC/B,SAAS,EAAC;QAAe;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,cAAc;QACjB,oBAAOvC,OAAA,CAACkC,aAAa;UAACC,IAAI,EAAE,EAAG;UAAC/B,SAAS,EAAC;QAAe;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9D,KAAK,eAAe;QAClB,oBAAOvC,OAAA,CAAC6C,SAAS;UAACV,IAAI,EAAE,EAAG;UAAC/B,SAAS,EAAC;QAAe;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAM2B,OAAO,gBACXlE,OAAA;IAAKI,SAAS,EAAE;AACpB;AACA;AACA;AACA;AACA,QAAQA,SAAS;AACjB,KAAM;IAAA+D,QAAA,GACCH,OAAO,CAAC,CAAC,eACVhE,OAAA;MAAMI,SAAS,EAAC,6BAA6B;MAAA+D,QAAA,EAAEhE;IAAK;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC5DvC,OAAA,CAACJ,WAAW;MACVuC,IAAI,EAAE,EAAG;MACT/B,SAAS,EAAC;IAAoG;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/G,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACEvC,OAAA,CAACH,QAAQ;IACPqE,OAAO,EAAEA,OAAQ;IACjB5D,KAAK,EAAEA,KAAM;IACbE,OAAO,EAAEA,OAAQ;IACjB4D,MAAM,EAAEzD,SAAU;IAClB0D,WAAW,EAAE,IAAK;IAClBC,YAAY,EAAE,IAAK;IACnBC,SAAS,EAAC,aAAa;IACvBnE,SAAS,EAAC,OAAO;IACjBoE,iBAAiB,EAAC,2BAA2B;IAC7CC,YAAY,EAAE,MAAMvE,IAAI;EAAa;IAAAkC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtC,CAAC;AAEN,CAAC;AAAClC,EAAA,CAhJIJ,kBAAqD;EAAA,QAOhCH,WAAW;AAAA;AAAA4E,EAAA,GAPhCzE,kBAAqD;AAkJ3D,eAAeA,kBAAkB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}