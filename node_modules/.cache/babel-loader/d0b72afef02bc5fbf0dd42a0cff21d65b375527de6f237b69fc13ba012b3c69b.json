{"ast": null, "code": "/**\n * Data Prefetcher Utility\n * Intelligently prefetches data for better user experience\n */\n\nimport sidebarService from '../services/sidebarService';\nclass DataPrefetcher {\n  constructor() {\n    this.prefetchQueue = [];\n    this.isProcessing = false;\n    this.prefetchedUrls = new Set();\n  }\n  /**\n   * Add items to prefetch queue\n   */\n  addToPrefetchQueue(items) {\n    this.prefetchQueue.push(...items);\n    this.processPrefetchQueue();\n  }\n\n  /**\n   * Prefetch data for common navigation patterns\n   */\n  prefetchCommonRoutes() {\n    const commonConfigs = [\n    // Prefetch countries sidebar data\n    {\n      config: {\n        type: 'countries',\n        limit: 15\n      },\n      options: {\n        priority: 'high',\n        delay: 100\n      }\n    },\n    // Prefetch levels sidebar data\n    {\n      config: {\n        type: 'levels',\n        limit: 10\n      },\n      options: {\n        priority: 'high',\n        delay: 200\n      }\n    },\n    // Prefetch opportunities sidebar data\n    {\n      config: {\n        type: 'opportunities',\n        limit: 10\n      },\n      options: {\n        priority: 'medium',\n        delay: 300\n      }\n    },\n    // Prefetch latest scholarships\n    {\n      config: {\n        type: 'countries'\n      },\n      // Will fetch latest scholarships\n      options: {\n        priority: 'medium',\n        delay: 500\n      }\n    }];\n    this.addToPrefetchQueue(commonConfigs);\n  }\n\n  /**\n   * Prefetch data based on user's current page\n   */\n  prefetchRelatedData(currentPageType, currentItem) {\n    const relatedConfigs = [];\n    switch (currentPageType) {\n      case 'country':\n        if (currentItem) {\n          // Prefetch other countries and latest scholarships\n          relatedConfigs.push({\n            config: {\n              type: 'countries',\n              currentItem,\n              limit: 15\n            },\n            options: {\n              priority: 'high',\n              delay: 0\n            }\n          }, {\n            config: {\n              type: 'levels',\n              limit: 10\n            },\n            options: {\n              priority: 'medium',\n              delay: 200\n            }\n          });\n        }\n        break;\n      case 'level':\n        if (currentItem) {\n          // Prefetch other levels and latest scholarships\n          relatedConfigs.push({\n            config: {\n              type: 'levels',\n              currentItem,\n              limit: 10\n            },\n            options: {\n              priority: 'high',\n              delay: 0\n            }\n          }, {\n            config: {\n              type: 'countries',\n              limit: 15\n            },\n            options: {\n              priority: 'medium',\n              delay: 200\n            }\n          });\n        }\n        break;\n      case 'opportunity':\n        if (currentItem) {\n          // Prefetch other opportunity types and latest opportunities\n          relatedConfigs.push({\n            config: {\n              type: 'opportunities',\n              currentItem,\n              limit: 10\n            },\n            options: {\n              priority: 'high',\n              delay: 0\n            }\n          });\n        }\n        break;\n    }\n    this.addToPrefetchQueue(relatedConfigs);\n  }\n\n  /**\n   * Prefetch data when user hovers over links\n   */\n  prefetchOnHover(targetConfig) {\n    const prefetchItem = {\n      config: targetConfig,\n      options: {\n        priority: 'medium',\n        delay: 100,\n        condition: () => this.shouldPrefetch()\n      }\n    };\n    this.addToPrefetchQueue([prefetchItem]);\n  }\n\n  /**\n   * Process the prefetch queue with priority and timing\n   */\n  async processPrefetchQueue() {\n    if (this.isProcessing || this.prefetchQueue.length === 0) {\n      return;\n    }\n    this.isProcessing = true;\n\n    // Sort by priority\n    this.prefetchQueue.sort((a, b) => {\n      const priorityOrder = {\n        high: 3,\n        medium: 2,\n        low: 1\n      };\n      return priorityOrder[b.options.priority] - priorityOrder[a.options.priority];\n    });\n    while (this.prefetchQueue.length > 0) {\n      const item = this.prefetchQueue.shift();\n      if (!item) continue;\n      try {\n        // Check condition if provided\n        if (item.options.condition && !item.options.condition()) {\n          continue;\n        }\n\n        // Apply delay if specified\n        if (item.options.delay) {\n          await new Promise(resolve => setTimeout(resolve, item.options.delay));\n        }\n\n        // Check if we should still prefetch (user might have navigated away)\n        if (!this.shouldPrefetch()) {\n          break;\n        }\n\n        // Generate cache key to avoid duplicate prefetches\n        const cacheKey = this.generateCacheKey(item.config);\n        if (this.prefetchedUrls.has(cacheKey)) {\n          continue;\n        }\n\n        // Prefetch the data\n        await sidebarService.fetchSidebarData(item.config);\n        this.prefetchedUrls.add(cacheKey);\n        console.log(`Prefetched data for ${item.config.type}:`, item.config);\n      } catch (error) {\n        console.warn('Prefetch failed:', error);\n      }\n\n      // Small delay between prefetches to avoid overwhelming the server\n      await new Promise(resolve => setTimeout(resolve, 50));\n    }\n    this.isProcessing = false;\n  }\n\n  /**\n   * Check if we should continue prefetching\n   */\n  shouldPrefetch() {\n    // Don't prefetch if user is on slow connection\n    if ('connection' in navigator) {\n      const connection = navigator.connection;\n      if (connection && (connection.saveData || connection.effectiveType === 'slow-2g')) {\n        return false;\n      }\n    }\n\n    // Don't prefetch if page is not visible\n    if (document.hidden) {\n      return false;\n    }\n\n    // Don't prefetch if user is idle for too long\n    return true;\n  }\n\n  /**\n   * Generate cache key for deduplication\n   */\n  generateCacheKey(config) {\n    const parts = [config.type];\n    if (config.currentItem) parts.push(config.currentItem);\n    if (config.excludeId) parts.push(`exclude-${config.excludeId}`);\n    if (config.limit) parts.push(`limit-${config.limit}`);\n    return parts.join(':');\n  }\n\n  /**\n   * Clear prefetch cache\n   */\n  clearPrefetchCache() {\n    this.prefetchedUrls.clear();\n    this.prefetchQueue.length = 0;\n  }\n\n  /**\n   * Get prefetch statistics\n   */\n  getPrefetchStats() {\n    return {\n      queueSize: this.prefetchQueue.length,\n      prefetchedCount: this.prefetchedUrls.size\n    };\n  }\n}\n\n// Export singleton instance\nexport const dataPrefetcher = new DataPrefetcher();\nexport default dataPrefetcher;", "map": {"version": 3, "names": ["sidebarService", "DataPrefetcher", "constructor", "prefetchQueue", "isProcessing", "prefetchedUrls", "Set", "addToPrefetchQueue", "items", "push", "processPrefetchQueue", "prefetchCommonRoutes", "commonConfigs", "config", "type", "limit", "options", "priority", "delay", "prefetchRelatedData", "currentPageType", "currentItem", "relatedConfigs", "prefetchOnHover", "targetConfig", "prefetchItem", "condition", "shouldPrefetch", "length", "sort", "a", "b", "priorityOrder", "high", "medium", "low", "item", "shift", "Promise", "resolve", "setTimeout", "cache<PERSON>ey", "generate<PERSON>ache<PERSON>ey", "has", "fetchSidebarData", "add", "console", "log", "error", "warn", "navigator", "connection", "saveData", "effectiveType", "document", "hidden", "parts", "excludeId", "join", "clearPrefetchCache", "clear", "getPrefetchStats", "queueSize", "prefetchedCount", "size", "dataPrefetcher"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dataPrefetcher.ts"], "sourcesContent": ["/**\n * Data Prefetcher Utility\n * Intelligently prefetches data for better user experience\n */\n\nimport sidebarService, { SidebarConfig } from '../services/sidebarService';\n\ninterface PrefetchConfig {\n  priority: 'high' | 'medium' | 'low';\n  delay?: number;\n  condition?: () => boolean;\n}\n\ninterface PrefetchItem {\n  config: SidebarConfig;\n  options: PrefetchConfig;\n}\n\nclass DataPrefetcher {\n  private prefetchQueue: PrefetchItem[] = [];\n  private isProcessing = false;\n  private prefetchedUrls = new Set<string>();\n\n  /**\n   * Add items to prefetch queue\n   */\n  public addToPrefetchQueue(items: PrefetchItem[]): void {\n    this.prefetchQueue.push(...items);\n    this.processPrefetchQueue();\n  }\n\n  /**\n   * Prefetch data for common navigation patterns\n   */\n  public prefetchCommonRoutes(): void {\n    const commonConfigs: PrefetchItem[] = [\n      // Prefetch countries sidebar data\n      {\n        config: { type: 'countries', limit: 15 },\n        options: { priority: 'high', delay: 100 }\n      },\n      // Prefetch levels sidebar data\n      {\n        config: { type: 'levels', limit: 10 },\n        options: { priority: 'high', delay: 200 }\n      },\n      // Prefetch opportunities sidebar data\n      {\n        config: { type: 'opportunities', limit: 10 },\n        options: { priority: 'medium', delay: 300 }\n      },\n      // Prefetch latest scholarships\n      {\n        config: { type: 'countries' }, // Will fetch latest scholarships\n        options: { priority: 'medium', delay: 500 }\n      }\n    ];\n\n    this.addToPrefetchQueue(commonConfigs);\n  }\n\n  /**\n   * Prefetch data based on user's current page\n   */\n  public prefetchRelatedData(currentPageType: string, currentItem?: string): void {\n    const relatedConfigs: PrefetchItem[] = [];\n\n    switch (currentPageType) {\n      case 'country':\n        if (currentItem) {\n          // Prefetch other countries and latest scholarships\n          relatedConfigs.push(\n            {\n              config: { type: 'countries', currentItem, limit: 15 },\n              options: { priority: 'high', delay: 0 }\n            },\n            {\n              config: { type: 'levels', limit: 10 },\n              options: { priority: 'medium', delay: 200 }\n            }\n          );\n        }\n        break;\n\n      case 'level':\n        if (currentItem) {\n          // Prefetch other levels and latest scholarships\n          relatedConfigs.push(\n            {\n              config: { type: 'levels', currentItem, limit: 10 },\n              options: { priority: 'high', delay: 0 }\n            },\n            {\n              config: { type: 'countries', limit: 15 },\n              options: { priority: 'medium', delay: 200 }\n            }\n          );\n        }\n        break;\n\n      case 'opportunity':\n        if (currentItem) {\n          // Prefetch other opportunity types and latest opportunities\n          relatedConfigs.push(\n            {\n              config: { type: 'opportunities', currentItem, limit: 10 },\n              options: { priority: 'high', delay: 0 }\n            }\n          );\n        }\n        break;\n    }\n\n    this.addToPrefetchQueue(relatedConfigs);\n  }\n\n  /**\n   * Prefetch data when user hovers over links\n   */\n  public prefetchOnHover(targetConfig: SidebarConfig): void {\n    const prefetchItem: PrefetchItem = {\n      config: targetConfig,\n      options: { \n        priority: 'medium', \n        delay: 100,\n        condition: () => this.shouldPrefetch()\n      }\n    };\n\n    this.addToPrefetchQueue([prefetchItem]);\n  }\n\n  /**\n   * Process the prefetch queue with priority and timing\n   */\n  private async processPrefetchQueue(): Promise<void> {\n    if (this.isProcessing || this.prefetchQueue.length === 0) {\n      return;\n    }\n\n    this.isProcessing = true;\n\n    // Sort by priority\n    this.prefetchQueue.sort((a, b) => {\n      const priorityOrder = { high: 3, medium: 2, low: 1 };\n      return priorityOrder[b.options.priority] - priorityOrder[a.options.priority];\n    });\n\n    while (this.prefetchQueue.length > 0) {\n      const item = this.prefetchQueue.shift();\n      if (!item) continue;\n\n      try {\n        // Check condition if provided\n        if (item.options.condition && !item.options.condition()) {\n          continue;\n        }\n\n        // Apply delay if specified\n        if (item.options.delay) {\n          await new Promise(resolve => setTimeout(resolve, item.options.delay));\n        }\n\n        // Check if we should still prefetch (user might have navigated away)\n        if (!this.shouldPrefetch()) {\n          break;\n        }\n\n        // Generate cache key to avoid duplicate prefetches\n        const cacheKey = this.generateCacheKey(item.config);\n        if (this.prefetchedUrls.has(cacheKey)) {\n          continue;\n        }\n\n        // Prefetch the data\n        await sidebarService.fetchSidebarData(item.config);\n        this.prefetchedUrls.add(cacheKey);\n\n        console.log(`Prefetched data for ${item.config.type}:`, item.config);\n\n      } catch (error) {\n        console.warn('Prefetch failed:', error);\n      }\n\n      // Small delay between prefetches to avoid overwhelming the server\n      await new Promise(resolve => setTimeout(resolve, 50));\n    }\n\n    this.isProcessing = false;\n  }\n\n  /**\n   * Check if we should continue prefetching\n   */\n  private shouldPrefetch(): boolean {\n    // Don't prefetch if user is on slow connection\n    if ('connection' in navigator) {\n      const connection = (navigator as any).connection;\n      if (connection && (connection.saveData || connection.effectiveType === 'slow-2g')) {\n        return false;\n      }\n    }\n\n    // Don't prefetch if page is not visible\n    if (document.hidden) {\n      return false;\n    }\n\n    // Don't prefetch if user is idle for too long\n    return true;\n  }\n\n  /**\n   * Generate cache key for deduplication\n   */\n  private generateCacheKey(config: SidebarConfig): string {\n    const parts: string[] = [config.type];\n    if (config.currentItem) parts.push(config.currentItem);\n    if (config.excludeId) parts.push(`exclude-${config.excludeId}`);\n    if (config.limit) parts.push(`limit-${config.limit}`);\n    return parts.join(':');\n  }\n\n  /**\n   * Clear prefetch cache\n   */\n  public clearPrefetchCache(): void {\n    this.prefetchedUrls.clear();\n    this.prefetchQueue.length = 0;\n  }\n\n  /**\n   * Get prefetch statistics\n   */\n  public getPrefetchStats(): { queueSize: number; prefetchedCount: number } {\n    return {\n      queueSize: this.prefetchQueue.length,\n      prefetchedCount: this.prefetchedUrls.size\n    };\n  }\n}\n\n// Export singleton instance\nexport const dataPrefetcher = new DataPrefetcher();\nexport default dataPrefetcher;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,cAAc,MAAyB,4BAA4B;AAa1E,MAAMC,cAAc,CAAC;EAAAC,YAAA;IAAA,KACXC,aAAa,GAAmB,EAAE;IAAA,KAClCC,YAAY,GAAG,KAAK;IAAA,KACpBC,cAAc,GAAG,IAAIC,GAAG,CAAS,CAAC;EAAA;EAE1C;AACF;AACA;EACSC,kBAAkBA,CAACC,KAAqB,EAAQ;IACrD,IAAI,CAACL,aAAa,CAACM,IAAI,CAAC,GAAGD,KAAK,CAAC;IACjC,IAAI,CAACE,oBAAoB,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;EACSC,oBAAoBA,CAAA,EAAS;IAClC,MAAMC,aAA6B,GAAG;IACpC;IACA;MACEC,MAAM,EAAE;QAAEC,IAAI,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAG,CAAC;MACxCC,OAAO,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAI;IAC1C,CAAC;IACD;IACA;MACEL,MAAM,EAAE;QAAEC,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAG,CAAC;MACrCC,OAAO,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAI;IAC1C,CAAC;IACD;IACA;MACEL,MAAM,EAAE;QAAEC,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE;MAAG,CAAC;MAC5CC,OAAO,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAI;IAC5C,CAAC;IACD;IACA;MACEL,MAAM,EAAE;QAAEC,IAAI,EAAE;MAAY,CAAC;MAAE;MAC/BE,OAAO,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAI;IAC5C,CAAC,CACF;IAED,IAAI,CAACX,kBAAkB,CAACK,aAAa,CAAC;EACxC;;EAEA;AACF;AACA;EACSO,mBAAmBA,CAACC,eAAuB,EAAEC,WAAoB,EAAQ;IAC9E,MAAMC,cAA8B,GAAG,EAAE;IAEzC,QAAQF,eAAe;MACrB,KAAK,SAAS;QACZ,IAAIC,WAAW,EAAE;UACf;UACAC,cAAc,CAACb,IAAI,CACjB;YACEI,MAAM,EAAE;cAAEC,IAAI,EAAE,WAAW;cAAEO,WAAW;cAAEN,KAAK,EAAE;YAAG,CAAC;YACrDC,OAAO,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAE;UACxC,CAAC,EACD;YACEL,MAAM,EAAE;cAAEC,IAAI,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAG,CAAC;YACrCC,OAAO,EAAE;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAI;UAC5C,CACF,CAAC;QACH;QACA;MAEF,KAAK,OAAO;QACV,IAAIG,WAAW,EAAE;UACf;UACAC,cAAc,CAACb,IAAI,CACjB;YACEI,MAAM,EAAE;cAAEC,IAAI,EAAE,QAAQ;cAAEO,WAAW;cAAEN,KAAK,EAAE;YAAG,CAAC;YAClDC,OAAO,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAE;UACxC,CAAC,EACD;YACEL,MAAM,EAAE;cAAEC,IAAI,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAG,CAAC;YACxCC,OAAO,EAAE;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAI;UAC5C,CACF,CAAC;QACH;QACA;MAEF,KAAK,aAAa;QAChB,IAAIG,WAAW,EAAE;UACf;UACAC,cAAc,CAACb,IAAI,CACjB;YACEI,MAAM,EAAE;cAAEC,IAAI,EAAE,eAAe;cAAEO,WAAW;cAAEN,KAAK,EAAE;YAAG,CAAC;YACzDC,OAAO,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAE;UACxC,CACF,CAAC;QACH;QACA;IACJ;IAEA,IAAI,CAACX,kBAAkB,CAACe,cAAc,CAAC;EACzC;;EAEA;AACF;AACA;EACSC,eAAeA,CAACC,YAA2B,EAAQ;IACxD,MAAMC,YAA0B,GAAG;MACjCZ,MAAM,EAAEW,YAAY;MACpBR,OAAO,EAAE;QACPC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,GAAG;QACVQ,SAAS,EAAEA,CAAA,KAAM,IAAI,CAACC,cAAc,CAAC;MACvC;IACF,CAAC;IAED,IAAI,CAACpB,kBAAkB,CAAC,CAACkB,YAAY,CAAC,CAAC;EACzC;;EAEA;AACF;AACA;EACE,MAAcf,oBAAoBA,CAAA,EAAkB;IAClD,IAAI,IAAI,CAACN,YAAY,IAAI,IAAI,CAACD,aAAa,CAACyB,MAAM,KAAK,CAAC,EAAE;MACxD;IACF;IAEA,IAAI,CAACxB,YAAY,GAAG,IAAI;;IAExB;IACA,IAAI,CAACD,aAAa,CAAC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAChC,MAAMC,aAAa,GAAG;QAAEC,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACpD,OAAOH,aAAa,CAACD,CAAC,CAACf,OAAO,CAACC,QAAQ,CAAC,GAAGe,aAAa,CAACF,CAAC,CAACd,OAAO,CAACC,QAAQ,CAAC;IAC9E,CAAC,CAAC;IAEF,OAAO,IAAI,CAACd,aAAa,CAACyB,MAAM,GAAG,CAAC,EAAE;MACpC,MAAMQ,IAAI,GAAG,IAAI,CAACjC,aAAa,CAACkC,KAAK,CAAC,CAAC;MACvC,IAAI,CAACD,IAAI,EAAE;MAEX,IAAI;QACF;QACA,IAAIA,IAAI,CAACpB,OAAO,CAACU,SAAS,IAAI,CAACU,IAAI,CAACpB,OAAO,CAACU,SAAS,CAAC,CAAC,EAAE;UACvD;QACF;;QAEA;QACA,IAAIU,IAAI,CAACpB,OAAO,CAACE,KAAK,EAAE;UACtB,MAAM,IAAIoB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEH,IAAI,CAACpB,OAAO,CAACE,KAAK,CAAC,CAAC;QACvE;;QAEA;QACA,IAAI,CAAC,IAAI,CAACS,cAAc,CAAC,CAAC,EAAE;UAC1B;QACF;;QAEA;QACA,MAAMc,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACN,IAAI,CAACvB,MAAM,CAAC;QACnD,IAAI,IAAI,CAACR,cAAc,CAACsC,GAAG,CAACF,QAAQ,CAAC,EAAE;UACrC;QACF;;QAEA;QACA,MAAMzC,cAAc,CAAC4C,gBAAgB,CAACR,IAAI,CAACvB,MAAM,CAAC;QAClD,IAAI,CAACR,cAAc,CAACwC,GAAG,CAACJ,QAAQ,CAAC;QAEjCK,OAAO,CAACC,GAAG,CAAC,uBAAuBX,IAAI,CAACvB,MAAM,CAACC,IAAI,GAAG,EAAEsB,IAAI,CAACvB,MAAM,CAAC;MAEtE,CAAC,CAAC,OAAOmC,KAAK,EAAE;QACdF,OAAO,CAACG,IAAI,CAAC,kBAAkB,EAAED,KAAK,CAAC;MACzC;;MAEA;MACA,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;IACvD;IAEA,IAAI,CAACnC,YAAY,GAAG,KAAK;EAC3B;;EAEA;AACF;AACA;EACUuB,cAAcA,CAAA,EAAY;IAChC;IACA,IAAI,YAAY,IAAIuB,SAAS,EAAE;MAC7B,MAAMC,UAAU,GAAID,SAAS,CAASC,UAAU;MAChD,IAAIA,UAAU,KAAKA,UAAU,CAACC,QAAQ,IAAID,UAAU,CAACE,aAAa,KAAK,SAAS,CAAC,EAAE;QACjF,OAAO,KAAK;MACd;IACF;;IAEA;IACA,IAAIC,QAAQ,CAACC,MAAM,EAAE;MACnB,OAAO,KAAK;IACd;;IAEA;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACUb,gBAAgBA,CAAC7B,MAAqB,EAAU;IACtD,MAAM2C,KAAe,GAAG,CAAC3C,MAAM,CAACC,IAAI,CAAC;IACrC,IAAID,MAAM,CAACQ,WAAW,EAAEmC,KAAK,CAAC/C,IAAI,CAACI,MAAM,CAACQ,WAAW,CAAC;IACtD,IAAIR,MAAM,CAAC4C,SAAS,EAAED,KAAK,CAAC/C,IAAI,CAAC,WAAWI,MAAM,CAAC4C,SAAS,EAAE,CAAC;IAC/D,IAAI5C,MAAM,CAACE,KAAK,EAAEyC,KAAK,CAAC/C,IAAI,CAAC,SAASI,MAAM,CAACE,KAAK,EAAE,CAAC;IACrD,OAAOyC,KAAK,CAACE,IAAI,CAAC,GAAG,CAAC;EACxB;;EAEA;AACF;AACA;EACSC,kBAAkBA,CAAA,EAAS;IAChC,IAAI,CAACtD,cAAc,CAACuD,KAAK,CAAC,CAAC;IAC3B,IAAI,CAACzD,aAAa,CAACyB,MAAM,GAAG,CAAC;EAC/B;;EAEA;AACF;AACA;EACSiC,gBAAgBA,CAAA,EAAmD;IACxE,OAAO;MACLC,SAAS,EAAE,IAAI,CAAC3D,aAAa,CAACyB,MAAM;MACpCmC,eAAe,EAAE,IAAI,CAAC1D,cAAc,CAAC2D;IACvC,CAAC;EACH;AACF;;AAEA;AACA,OAAO,MAAMC,cAAc,GAAG,IAAIhE,cAAc,CAAC,CAAC;AAClD,eAAegE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}