{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport EnhancedScholarshipCard from './EnhancedScholarshipCard';\nimport { Government, Building, Organization } from './icons/index';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedFundingSourcesSection = ({\n  governmentScholarships,\n  universityScholarships,\n  organizationScholarships,\n  loading,\n  onScholarshipClick\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('government');\n\n  // Get active scholarships based on tab\n  const getActiveScholarships = () => {\n    switch (activeTab) {\n      case 'government':\n        return governmentScholarships;\n      case 'university':\n        return universityScholarships;\n      case 'organization':\n        return organizationScholarships;\n      default:\n        return governmentScholarships;\n    }\n  };\n  const activeScholarships = getActiveScholarships();\n\n  // Tab configuration\n  const tabs = [{\n    id: 'government',\n    label: 'Gouvernements',\n    icon: /*#__PURE__*/_jsxDEV(Government, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this),\n    color: 'blue',\n    description: 'Bourses financées par des gouvernements nationaux et internationaux',\n    bgClass: 'from-blue-500 to-blue-700'\n  }, {\n    id: 'university',\n    label: 'Universités',\n    icon: /*#__PURE__*/_jsxDEV(Building, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this),\n    color: 'purple',\n    description: 'Bourses offertes par des établissements d\\'enseignement supérieur',\n    bgClass: 'from-purple-500 to-purple-700'\n  }, {\n    id: 'organization',\n    label: 'Organisations',\n    icon: /*#__PURE__*/_jsxDEV(Organization, {\n      className: \"w-6 h-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    color: 'green',\n    description: 'Bourses proposées par des ONG, fondations et entreprises',\n    bgClass: 'from-green-500 to-green-700'\n  }];\n\n  // Get active tab data\n  const activeTabData = tabs.find(tab => tab.id === activeTab) || tabs[0];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-8 bg-gray-50 overflow-hidden\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Sources de Financement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap justify-center gap-4 mb-8\",\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.id),\n          className: `flex items-center px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 border ${activeTab === tab.id ? 'bg-primary text-white border-primary shadow-md' : 'bg-white text-gray-700 border-gray-200 hover:border-primary hover:text-primary'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-2\",\n            children: tab.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `relative rounded-xl overflow-hidden mb-5 bg-gradient-to-r ${activeTabData.bgClass}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 opacity-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-full h-full\",\n              viewBox: \"0 0 100 100\",\n              preserveAspectRatio: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M0,0 L100,0 L100,100 L0,100 Z\",\n                fill: \"url(#pattern)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n              children: /*#__PURE__*/_jsxDEV(\"pattern\", {\n                id: \"pattern\",\n                x: \"0\",\n                y: \"0\",\n                width: \"10\",\n                height: \"10\",\n                patternUnits: \"userSpaceOnUse\",\n                children: /*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"5\",\n                  cy: \"5\",\n                  r: \"1\",\n                  fill: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative py-4 px-6 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0 mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm\",\n                  children: activeTabData.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 119,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-bold\",\n                  children: [\"Bourses des \", activeTabData.label]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white/90 text-xs\",\n                  children: activeTabData.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: [...Array(6)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-md overflow-hidden animate-pulse\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"aspect-[16/9] bg-gray-200\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-5 bg-gray-200 rounded w-3/4 mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-1/2 mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-full mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-10 bg-gray-100 rounded w-full mt-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this) : activeScholarships.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: activeScholarships.slice(0, 6).map((scholarship, index) => /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n          id: scholarship.id,\n          title: scholarship.title,\n          thumbnail: scholarship.thumbnail,\n          deadline: scholarship.deadline,\n          isOpen: scholarship.isOpen,\n          level: scholarship.level,\n          fundingSource: activeTab === 'government' ? 'Gouvernement' : activeTab === 'university' ? 'Université' : 'Organisation',\n          country: scholarship.country,\n          onClick: onScholarshipClick,\n          index: index\n        }, scholarship.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12 bg-white rounded-2xl shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"mx-auto h-12 w-12 text-gray-400\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 1,\n            d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-lg font-medium text-gray-900\",\n          children: \"Aucune bourse trouv\\xE9e\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-gray-500\",\n          children: \"Aucune bourse n'est disponible pour cette source de financement.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: `/scholarships?source=${activeTab === 'government' ? 'Gouvernement' : activeTab === 'university' ? 'Université' : 'Organisation'}`,\n          className: `inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-xl text-white shadow-md transition-colors duration-300 ${activeTab === 'government' ? 'bg-blue-600 hover:bg-blue-700' : activeTab === 'university' ? 'bg-purple-600 hover:bg-purple-700' : 'bg-green-600 hover:bg-green-700'}`,\n          children: [\"Voir toutes les bourses \", activeTabData.label.toLowerCase(), /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4 ml-2\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedFundingSourcesSection, \"qzdoa2tSKAhnGe6mrzJI0157Sg0=\");\n_c = EnhancedFundingSourcesSection;\nexport default EnhancedFundingSourcesSection;\nvar _c;\n$RefreshReg$(_c, \"EnhancedFundingSourcesSection\");", "map": {"version": 3, "names": ["React", "useState", "Link", "EnhancedScholarshipCard", "Government", "Building", "Organization", "jsxDEV", "_jsxDEV", "EnhancedFundingSourcesSection", "governmentScholarships", "universityScholarships", "organizationScholarships", "loading", "onScholarshipClick", "_s", "activeTab", "setActiveTab", "getActiveScholarships", "activeScholarships", "tabs", "id", "label", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "description", "bgClass", "activeTabData", "find", "tab", "children", "map", "onClick", "viewBox", "preserveAspectRatio", "d", "fill", "x", "y", "width", "height", "patternUnits", "cx", "cy", "r", "Array", "_", "index", "length", "slice", "scholarship", "title", "thumbnail", "deadline", "isOpen", "level", "fundingSource", "country", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "to", "toLowerCase", "xmlns", "fillRule", "clipRule", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { Scholarship } from './ScholarshipGrid';\nimport EnhancedScholarshipCard from './EnhancedScholarshipCard';\nimport { Government, Building, Organization } from './icons/index';\n\ninterface EnhancedFundingSourcesSectionProps {\n  governmentScholarships: Scholarship[];\n  universityScholarships: Scholarship[];\n  organizationScholarships: Scholarship[];\n  loading: boolean;\n  onScholarshipClick: (id: number) => void;\n}\n\nconst EnhancedFundingSourcesSection: React.FC<EnhancedFundingSourcesSectionProps> = ({\n  governmentScholarships,\n  universityScholarships,\n  organizationScholarships,\n  loading,\n  onScholarshipClick\n}) => {\n  const [activeTab, setActiveTab] = useState('government');\n\n\n\n  // Get active scholarships based on tab\n  const getActiveScholarships = () => {\n    switch (activeTab) {\n      case 'government':\n        return governmentScholarships;\n      case 'university':\n        return universityScholarships;\n      case 'organization':\n        return organizationScholarships;\n      default:\n        return governmentScholarships;\n    }\n  };\n\n  const activeScholarships = getActiveScholarships();\n\n  // Tab configuration\n  const tabs = [\n    {\n      id: 'government',\n      label: 'Gouvernements',\n      icon: <Government className=\"w-6 h-6\" />,\n      color: 'blue',\n      description: 'Bourses financées par des gouvernements nationaux et internationaux',\n      bgClass: 'from-blue-500 to-blue-700'\n    },\n    {\n      id: 'university',\n      label: 'Universités',\n      icon: <Building className=\"w-6 h-6\" />,\n      color: 'purple',\n      description: 'Bourses offertes par des établissements d\\'enseignement supérieur',\n      bgClass: 'from-purple-500 to-purple-700'\n    },\n    {\n      id: 'organization',\n      label: 'Organisations',\n      icon: <Organization className=\"w-6 h-6\" />,\n      color: 'green',\n      description: 'Bourses proposées par des ONG, fondations et entreprises',\n      bgClass: 'from-green-500 to-green-700'\n    }\n  ];\n\n  // Get active tab data\n  const activeTabData = tabs.find(tab => tab.id === activeTab) || tabs[0];\n\n  return (\n    <section className=\"py-8 bg-gray-50 overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section header - Professional & Clean */}\n        <div className=\"text-center mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900\">\n            Sources de Financement\n          </h2>\n        </div>\n\n        {/* Interactive tabs */}\n        <div className=\"flex flex-wrap justify-center gap-4 mb-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`flex items-center px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 border ${\n                activeTab === tab.id\n                  ? 'bg-primary text-white border-primary shadow-md'\n                  : 'bg-white text-gray-700 border-gray-200 hover:border-primary hover:text-primary'\n              }`}\n            >\n              <span className=\"mr-2\">\n                {tab.icon}\n              </span>\n              {tab.label}\n            </button>\n          ))}\n        </div>\n\n        {/* Tab content with header */}\n        <div className=\"mb-5\">\n          <div className={`relative rounded-xl overflow-hidden mb-5 bg-gradient-to-r ${activeTabData.bgClass}`}>\n            <div className=\"absolute inset-0 opacity-20\">\n              <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\" preserveAspectRatio=\"none\">\n                <path d=\"M0,0 L100,0 L100,100 L0,100 Z\" fill=\"url(#pattern)\" />\n              </svg>\n              <defs>\n                <pattern id=\"pattern\" x=\"0\" y=\"0\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\">\n                  <circle cx=\"5\" cy=\"5\" r=\"1\" fill=\"white\" />\n                </pattern>\n              </defs>\n            </div>\n            <div className=\"relative py-4 px-6 text-white\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0 mr-3\">\n                  <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm\">\n                    {activeTabData.icon}\n                  </div>\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-bold\">Bourses des {activeTabData.label}</h3>\n                  <p className=\"text-white/90 text-xs\">\n                    {activeTabData.description}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scholarships grid - 3x2 grid */}\n        {loading ? (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[...Array(6)].map((_, index) => (\n              <div key={index} className=\"bg-white rounded-2xl shadow-md overflow-hidden animate-pulse\">\n                <div className=\"aspect-[16/9] bg-gray-200\"></div>\n                <div className=\"p-5\">\n                  <div className=\"h-5 bg-gray-200 rounded w-3/4 mb-3\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-3\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-full mb-3\"></div>\n                  <div className=\"h-10 bg-gray-100 rounded w-full mt-4\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : activeScholarships.length > 0 ? (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {activeScholarships.slice(0, 6).map((scholarship, index) => (\n              <EnhancedScholarshipCard\n                key={scholarship.id}\n                id={scholarship.id}\n                title={scholarship.title}\n                thumbnail={scholarship.thumbnail}\n                deadline={scholarship.deadline}\n                isOpen={scholarship.isOpen}\n                level={scholarship.level}\n                fundingSource={\n                  activeTab === 'government' ? 'Gouvernement' :\n                  activeTab === 'university' ? 'Université' : 'Organisation'\n                }\n                country={scholarship.country}\n                onClick={onScholarshipClick}\n                index={index}\n              />\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12 bg-white rounded-2xl shadow-sm\">\n            <svg className=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">Aucune bourse trouvée</h3>\n            <p className=\"mt-1 text-gray-500\">Aucune bourse n'est disponible pour cette source de financement.</p>\n          </div>\n        )}\n\n        {/* Call to action */}\n        <div className=\"mt-6 flex justify-center\">\n          <Link\n            to={`/scholarships?source=${\n              activeTab === 'government' ? 'Gouvernement' :\n              activeTab === 'university' ? 'Université' : 'Organisation'\n            }`}\n            className={`inline-flex items-center px-5 py-2 border border-transparent text-sm font-medium rounded-xl text-white shadow-md transition-colors duration-300 ${\n              activeTab === 'government' ? 'bg-blue-600 hover:bg-blue-700' :\n              activeTab === 'university' ? 'bg-purple-600 hover:bg-purple-700' :\n              'bg-green-600 hover:bg-green-700'\n            }`}\n          >\n            Voir toutes les bourses {activeTabData.label.toLowerCase()}\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default EnhancedFundingSourcesSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AAEvC,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,SAASC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUnE,MAAMC,6BAA2E,GAAGA,CAAC;EACnFC,sBAAsB;EACtBC,sBAAsB;EACtBC,wBAAwB;EACxBC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,YAAY,CAAC;;EAIxD;EACA,MAAMiB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,QAAQF,SAAS;MACf,KAAK,YAAY;QACf,OAAON,sBAAsB;MAC/B,KAAK,YAAY;QACf,OAAOC,sBAAsB;MAC/B,KAAK,cAAc;QACjB,OAAOC,wBAAwB;MACjC;QACE,OAAOF,sBAAsB;IACjC;EACF,CAAC;EAED,MAAMS,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC;;EAElD;EACA,MAAME,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEf,OAAA,CAACJ,UAAU;MAACoB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxCC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,qEAAqE;IAClFC,OAAO,EAAE;EACX,CAAC,EACD;IACEV,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEf,OAAA,CAACH,QAAQ;MAACmB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,mEAAmE;IAChFC,OAAO,EAAE;EACX,CAAC,EACD;IACEV,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEf,OAAA,CAACF,YAAY;MAACkB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1CC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,0DAA0D;IACvEC,OAAO,EAAE;EACX,CAAC,CACF;;EAED;EACA,MAAMC,aAAa,GAAGZ,IAAI,CAACa,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACb,EAAE,KAAKL,SAAS,CAAC,IAAII,IAAI,CAAC,CAAC,CAAC;EAEvE,oBACEZ,OAAA;IAASgB,SAAS,EAAC,iCAAiC;IAAAW,QAAA,eAClD3B,OAAA;MAAKgB,SAAS,EAAC,wCAAwC;MAAAW,QAAA,gBAErD3B,OAAA;QAAKgB,SAAS,EAAC,kBAAkB;QAAAW,QAAA,eAC/B3B,OAAA;UAAIgB,SAAS,EAAC,kCAAkC;UAAAW,QAAA,EAAC;QAEjD;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNpB,OAAA;QAAKgB,SAAS,EAAC,0CAA0C;QAAAW,QAAA,EACtDf,IAAI,CAACgB,GAAG,CAAEF,GAAG,iBACZ1B,OAAA;UAEE6B,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAACiB,GAAG,CAACb,EAAE,CAAE;UACpCG,SAAS,EAAE,iGACTR,SAAS,KAAKkB,GAAG,CAACb,EAAE,GAChB,gDAAgD,GAChD,gFAAgF,EACnF;UAAAc,QAAA,gBAEH3B,OAAA;YAAMgB,SAAS,EAAC,MAAM;YAAAW,QAAA,EACnBD,GAAG,CAACX;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACNM,GAAG,CAACZ,KAAK;QAAA,GAXLY,GAAG,CAACb,EAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYL,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNpB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAW,QAAA,eACnB3B,OAAA;UAAKgB,SAAS,EAAE,6DAA6DQ,aAAa,CAACD,OAAO,EAAG;UAAAI,QAAA,gBACnG3B,OAAA;YAAKgB,SAAS,EAAC,6BAA6B;YAAAW,QAAA,gBAC1C3B,OAAA;cAAKgB,SAAS,EAAC,eAAe;cAACc,OAAO,EAAC,aAAa;cAACC,mBAAmB,EAAC,MAAM;cAAAJ,QAAA,eAC7E3B,OAAA;gBAAMgC,CAAC,EAAC,+BAA+B;gBAACC,IAAI,EAAC;cAAe;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACNpB,OAAA;cAAA2B,QAAA,eACE3B,OAAA;gBAASa,EAAE,EAAC,SAAS;gBAACqB,CAAC,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAACC,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,YAAY,EAAC,gBAAgB;gBAAAX,QAAA,eACpF3B,OAAA;kBAAQuC,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACR,IAAI,EAAC;gBAAO;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNpB,OAAA;YAAKgB,SAAS,EAAC,+BAA+B;YAAAW,QAAA,eAC5C3B,OAAA;cAAKgB,SAAS,EAAC,mBAAmB;cAAAW,QAAA,gBAChC3B,OAAA;gBAAKgB,SAAS,EAAC,oBAAoB;gBAAAW,QAAA,eACjC3B,OAAA;kBAAKgB,SAAS,EAAC,sFAAsF;kBAAAW,QAAA,EAClGH,aAAa,CAACT;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpB,OAAA;gBAAA2B,QAAA,gBACE3B,OAAA;kBAAIgB,SAAS,EAAC,mBAAmB;kBAAAW,QAAA,GAAC,cAAY,EAACH,aAAa,CAACV,KAAK;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxEpB,OAAA;kBAAGgB,SAAS,EAAC,uBAAuB;kBAAAW,QAAA,EACjCH,aAAa,CAACF;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLf,OAAO,gBACNL,OAAA;QAAKgB,SAAS,EAAC,sDAAsD;QAAAW,QAAA,EAClE,CAAC,GAAGe,KAAK,CAAC,CAAC,CAAC,CAAC,CAACd,GAAG,CAAC,CAACe,CAAC,EAAEC,KAAK,kBAC1B5C,OAAA;UAAiBgB,SAAS,EAAC,8DAA8D;UAAAW,QAAA,gBACvF3B,OAAA;YAAKgB,SAAS,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDpB,OAAA;YAAKgB,SAAS,EAAC,KAAK;YAAAW,QAAA,gBAClB3B,OAAA;cAAKgB,SAAS,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DpB,OAAA;cAAKgB,SAAS,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DpB,OAAA;cAAKgB,SAAS,EAAC;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DpB,OAAA;cAAKgB,SAAS,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA,GAPEwB,KAAK;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,GACJT,kBAAkB,CAACkC,MAAM,GAAG,CAAC,gBAC/B7C,OAAA;QAAKgB,SAAS,EAAC,sDAAsD;QAAAW,QAAA,EAClEhB,kBAAkB,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClB,GAAG,CAAC,CAACmB,WAAW,EAAEH,KAAK,kBACrD5C,OAAA,CAACL,uBAAuB;UAEtBkB,EAAE,EAAEkC,WAAW,CAAClC,EAAG;UACnBmC,KAAK,EAAED,WAAW,CAACC,KAAM;UACzBC,SAAS,EAAEF,WAAW,CAACE,SAAU;UACjCC,QAAQ,EAAEH,WAAW,CAACG,QAAS;UAC/BC,MAAM,EAAEJ,WAAW,CAACI,MAAO;UAC3BC,KAAK,EAAEL,WAAW,CAACK,KAAM;UACzBC,aAAa,EACX7C,SAAS,KAAK,YAAY,GAAG,cAAc,GAC3CA,SAAS,KAAK,YAAY,GAAG,YAAY,GAAG,cAC7C;UACD8C,OAAO,EAAEP,WAAW,CAACO,OAAQ;UAC7BzB,OAAO,EAAEvB,kBAAmB;UAC5BsC,KAAK,EAAEA;QAAM,GAbRG,WAAW,CAAClC,EAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcpB,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENpB,OAAA;QAAKgB,SAAS,EAAC,kDAAkD;QAAAW,QAAA,gBAC/D3B,OAAA;UAAKgB,SAAS,EAAC,iCAAiC;UAACiB,IAAI,EAAC,MAAM;UAACH,OAAO,EAAC,WAAW;UAACyB,MAAM,EAAC,cAAc;UAAA5B,QAAA,eACpG3B,OAAA;YAAMwD,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAAC1B,CAAC,EAAC;UAAoF;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzJ,CAAC,eACNpB,OAAA;UAAIgB,SAAS,EAAC,wCAAwC;UAAAW,QAAA,EAAC;QAAqB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFpB,OAAA;UAAGgB,SAAS,EAAC,oBAAoB;UAAAW,QAAA,EAAC;QAAgE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CACN,eAGDpB,OAAA;QAAKgB,SAAS,EAAC,0BAA0B;QAAAW,QAAA,eACvC3B,OAAA,CAACN,IAAI;UACHiE,EAAE,EAAE,wBACFnD,SAAS,KAAK,YAAY,GAAG,cAAc,GAC3CA,SAAS,KAAK,YAAY,GAAG,YAAY,GAAG,cAAc,EACzD;UACHQ,SAAS,EAAE,mJACTR,SAAS,KAAK,YAAY,GAAG,+BAA+B,GAC5DA,SAAS,KAAK,YAAY,GAAG,mCAAmC,GAChE,iCAAiC,EAChC;UAAAmB,QAAA,GACJ,0BACyB,EAACH,aAAa,CAACV,KAAK,CAAC8C,WAAW,CAAC,CAAC,eAC1D5D,OAAA;YAAK6D,KAAK,EAAC,4BAA4B;YAAC7C,SAAS,EAAC,cAAc;YAACc,OAAO,EAAC,WAAW;YAACG,IAAI,EAAC,cAAc;YAAAN,QAAA,eACtG3B,OAAA;cAAM8D,QAAQ,EAAC,SAAS;cAAC9B,CAAC,EAAC,yIAAyI;cAAC+B,QAAQ,EAAC;YAAS;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACb,EAAA,CA3LIN,6BAA2E;AAAA+D,EAAA,GAA3E/D,6BAA2E;AA6LjF,eAAeA,6BAA6B;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}