{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 12h11\",\n  key: \"6m4ad9\"\n}], [\"path\", {\n  d: \"M10 18h11\",\n  key: \"11hvi2\"\n}], [\"path\", {\n  d: \"M10 6h11\",\n  key: \"c7qv1k\"\n}], [\"path\", {\n  d: \"m3 10 3-3-3-3\",\n  key: \"i7pm08\"\n}], [\"path\", {\n  d: \"m3 20 3-3-3-3\",\n  key: \"20gx1n\"\n}]];\nconst ListCollapse = createLucideIcon(\"list-collapse\", __iconNode);\nexport { __iconNode, ListCollapse as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ListCollapse", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/list-collapse.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 12h11', key: '6m4ad9' }],\n  ['path', { d: 'M10 18h11', key: '11hvi2' }],\n  ['path', { d: 'M10 6h11', key: 'c7qv1k' }],\n  ['path', { d: 'm3 10 3-3-3-3', key: 'i7pm08' }],\n  ['path', { d: 'm3 20 3-3-3-3', key: '20gx1n' }],\n];\n\n/**\n * @component @name ListCollapse\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTJoMTEiIC8+CiAgPHBhdGggZD0iTTEwIDE4aDExIiAvPgogIDxwYXRoIGQ9Ik0xMCA2aDExIiAvPgogIDxwYXRoIGQ9Im0zIDEwIDMtMy0zLTMiIC8+CiAgPHBhdGggZD0ibTMgMjAgMy0zLTMtMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/list-collapse\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ListCollapse = createLucideIcon('list-collapse', __iconNode);\n\nexport default ListCollapse;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAU,GAChD;AAaM,MAAAC,YAAA,GAAeC,gBAAiB,kBAAiBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}