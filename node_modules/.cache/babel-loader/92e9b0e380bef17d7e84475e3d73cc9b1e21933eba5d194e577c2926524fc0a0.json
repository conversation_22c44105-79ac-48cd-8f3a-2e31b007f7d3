{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport ScholarshipCard from '../components/ScholarshipCard';\nimport ProfessionalPageLayout, { ProfessionalContentGrid } from '../components/ProfessionalPageLayout';\nimport sidebarService from '../services/sidebarService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CountryDetail = () => {\n  _s();\n  const {\n    country\n  } = useParams();\n  const [scholarships, setScholarships] = useState([]);\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const decodedCountry = country ? decodeURIComponent(country) : '';\n  const fetchScholarships = React.useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '12'\n      });\n      const response = await fetch(`/api/countries/${encodeURIComponent(decodedCountry)}/scholarships?${params}`);\n      if (response.ok) {\n        var _data$pagination;\n        const data = await response.json();\n        setScholarships(data.data || []);\n        setTotalPages(((_data$pagination = data.pagination) === null || _data$pagination === void 0 ? void 0 : _data$pagination.totalPages) || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [decodedCountry, currentPage]);\n  const fetchStatistics = React.useCallback(async () => {\n    try {\n      const response = await fetch(`/api/countries/${encodeURIComponent(decodedCountry)}/statistics`);\n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching statistics:', error);\n    }\n  }, [decodedCountry]);\n  useEffect(() => {\n    if (decodedCountry) {\n      fetchScholarships();\n      fetchStatistics();\n    }\n  }, [decodedCountry, fetchScholarships, fetchStatistics]);\n  const handleScholarshipClick = id => {\n    window.location.href = `/scholarships/${id}`;\n  };\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const renderScholarshipItem = scholarship => /*#__PURE__*/_jsxDEV(ScholarshipCard, {\n    id: scholarship.id,\n    title: scholarship.title,\n    thumbnail: scholarship.thumbnail || '',\n    deadline: scholarship.deadline,\n    isOpen: scholarship.isOpen,\n    country: scholarship.country,\n    onClick: handleScholarshipClick\n  }, scholarship.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(ProfessionalPageLayout, {\n    hero: {\n      title: decodedCountry,\n      subtitle: \"Découvrez toutes les bourses d'études disponibles\",\n      icon: sidebarService.getCountryFlag(decodedCountry),\n      backgroundColor: 'bg-gradient-to-r from-blue-600 to-blue-800'\n    },\n    statistics: statistics ? {\n      total: statistics.totalScholarships,\n      active: statistics.openScholarships,\n      inactive: statistics.closedScholarships,\n      label: 'Total des bourses',\n      activeLabel: 'Bourses ouvertes',\n      inactiveLabel: 'Bourses fermées'\n    } : undefined,\n    sidebarConfig: {\n      type: 'countries',\n      currentItem: decodedCountry,\n      limit: 15\n    },\n    children: /*#__PURE__*/_jsxDEV(ProfessionalContentGrid, {\n      items: scholarships,\n      loading: loading,\n      emptyMessage: \"Aucune bourse n'est actuellement disponible pour ce pays.\",\n      emptyIcon: \"\\uD83D\\uDCDA\",\n      renderItem: renderScholarshipItem,\n      pagination: {\n        currentPage,\n        totalPages,\n        onPageChange: handlePageChange\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_s(CountryDetail, \"4Qjxq6/kKQmYVZ08nJnuZ0ejJPo=\", false, function () {\n  return [useParams];\n});\n_c = CountryDetail;\nexport default CountryDetail;\nvar _c;\n$RefreshReg$(_c, \"CountryDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "ScholarshipCard", "ProfessionalPageLayout", "ProfessionalContentGrid", "sidebarService", "jsxDEV", "_jsxDEV", "CountryDetail", "_s", "country", "scholarships", "setScholarships", "statistics", "setStatistics", "loading", "setLoading", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "decodedCountry", "decodeURIComponent", "fetchScholarships", "useCallback", "params", "URLSearchParams", "page", "toString", "limit", "response", "fetch", "encodeURIComponent", "ok", "_data$pagination", "data", "json", "pagination", "error", "console", "fetchStatistics", "handleScholarshipClick", "id", "window", "location", "href", "handlePageChange", "renderScholarshipItem", "scholarship", "title", "thumbnail", "deadline", "isOpen", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hero", "subtitle", "icon", "getCountryFlag", "backgroundColor", "total", "totalScholarships", "active", "openScholarships", "inactive", "closedScholarships", "label", "activeLabel", "inactiveLabel", "undefined", "sidebarConfig", "type", "currentItem", "children", "items", "emptyMessage", "emptyIcon", "renderItem", "onPageChange", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport ScholarshipCard from '../components/ScholarshipCard';\nimport ProfessionalPageLayout, { ProfessionalContentGrid } from '../components/ProfessionalPageLayout';\nimport sidebarService from '../services/sidebarService';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  deadline: string;\n  isOpen: boolean;\n  country: string;\n  level?: string;\n}\n\ninterface CountryStatistics {\n  country: string;\n  totalScholarships: number;\n  openScholarships: number;\n  closedScholarships: number;\n  scholarshipsByLevel: Array<{\n    level: string;\n    count: number;\n  }>;\n}\n\nconst CountryDetail: React.FC = () => {\n  const { country } = useParams<{ country: string }>();\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [statistics, setStatistics] = useState<CountryStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  const decodedCountry = country ? decodeURIComponent(country) : '';\n\n  const fetchScholarships = React.useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '12'\n      });\n\n      const response = await fetch(`/api/countries/${encodeURIComponent(decodedCountry)}/scholarships?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setScholarships(data.data || []);\n        setTotalPages(data.pagination?.totalPages || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [decodedCountry, currentPage]);\n\n  const fetchStatistics = React.useCallback(async () => {\n    try {\n      const response = await fetch(`/api/countries/${encodeURIComponent(decodedCountry)}/statistics`);\n      if (response.ok) {\n        const data = await response.json();\n        setStatistics(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching statistics:', error);\n    }\n  }, [decodedCountry]);\n\n  useEffect(() => {\n    if (decodedCountry) {\n      fetchScholarships();\n      fetchStatistics();\n    }\n  }, [decodedCountry, fetchScholarships, fetchStatistics]);\n\n\n\n  const handleScholarshipClick = (id: number) => {\n    window.location.href = `/scholarships/${id}`;\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const renderScholarshipItem = (scholarship: Scholarship) => (\n    <ScholarshipCard\n      key={scholarship.id}\n      id={scholarship.id}\n      title={scholarship.title}\n      thumbnail={scholarship.thumbnail || ''}\n      deadline={scholarship.deadline}\n      isOpen={scholarship.isOpen}\n      country={scholarship.country}\n      onClick={handleScholarshipClick}\n    />\n  );\n\n  return (\n    <ProfessionalPageLayout\n      hero={{\n        title: decodedCountry,\n        subtitle: \"Découvrez toutes les bourses d'études disponibles\",\n        icon: sidebarService.getCountryFlag(decodedCountry),\n        backgroundColor: 'bg-gradient-to-r from-blue-600 to-blue-800'\n      }}\n      statistics={statistics ? {\n        total: statistics.totalScholarships,\n        active: statistics.openScholarships,\n        inactive: statistics.closedScholarships,\n        label: 'Total des bourses',\n        activeLabel: 'Bourses ouvertes',\n        inactiveLabel: 'Bourses fermées'\n      } : undefined}\n      sidebarConfig={{\n        type: 'countries',\n        currentItem: decodedCountry,\n        limit: 15\n      }}\n    >\n      <ProfessionalContentGrid\n        items={scholarships}\n        loading={loading}\n        emptyMessage=\"Aucune bourse n'est actuellement disponible pour ce pays.\"\n        emptyIcon=\"📚\"\n        renderItem={renderScholarshipItem}\n        pagination={{\n          currentPage,\n          totalPages,\n          onPageChange: handlePageChange\n        }}\n      />\n    </ProfessionalPageLayout>\n\n  );\n};\n\nexport default CountryDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAE5C,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,sBAAsB,IAAIC,uBAAuB,QAAQ,sCAAsC;AACtG,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBxD,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAQ,CAAC,GAAGT,SAAS,CAAsB,CAAC;EACpD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAA2B,IAAI,CAAC;EAC5E,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMsB,cAAc,GAAGX,OAAO,GAAGY,kBAAkB,CAACZ,OAAO,CAAC,GAAG,EAAE;EAEjE,MAAMa,iBAAiB,GAAGzB,KAAK,CAAC0B,WAAW,CAAC,YAAY;IACtD,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMS,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,IAAI,EAAEV,WAAW,CAACW,QAAQ,CAAC,CAAC;QAC5BC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kBAAkBC,kBAAkB,CAACX,cAAc,CAAC,iBAAiBI,MAAM,EAAE,CAAC;MAC3G,IAAIK,QAAQ,CAACG,EAAE,EAAE;QAAA,IAAAC,gBAAA;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCxB,eAAe,CAACuB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAChCf,aAAa,CAAC,EAAAc,gBAAA,GAAAC,IAAI,CAACE,UAAU,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBf,UAAU,KAAI,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,cAAc,EAAEJ,WAAW,CAAC,CAAC;EAEjC,MAAMuB,eAAe,GAAG1C,KAAK,CAAC0B,WAAW,CAAC,YAAY;IACpD,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,kBAAkBC,kBAAkB,CAACX,cAAc,CAAC,aAAa,CAAC;MAC/F,IAAIS,QAAQ,CAACG,EAAE,EAAE;QACf,MAAME,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCtB,aAAa,CAACqB,IAAI,CAACA,IAAI,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC,EAAE,CAACjB,cAAc,CAAC,CAAC;EAEpBrB,SAAS,CAAC,MAAM;IACd,IAAIqB,cAAc,EAAE;MAClBE,iBAAiB,CAAC,CAAC;MACnBiB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACnB,cAAc,EAAEE,iBAAiB,EAAEiB,eAAe,CAAC,CAAC;EAIxD,MAAMC,sBAAsB,GAAIC,EAAU,IAAK;IAC7CC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,iBAAiBH,EAAE,EAAE;EAC9C,CAAC;EAED,MAAMI,gBAAgB,GAAInB,IAAY,IAAK;IACzCT,cAAc,CAACS,IAAI,CAAC;EACtB,CAAC;EAED,MAAMoB,qBAAqB,GAAIC,WAAwB,iBACrDzC,OAAA,CAACL,eAAe;IAEdwC,EAAE,EAAEM,WAAW,CAACN,EAAG;IACnBO,KAAK,EAAED,WAAW,CAACC,KAAM;IACzBC,SAAS,EAAEF,WAAW,CAACE,SAAS,IAAI,EAAG;IACvCC,QAAQ,EAAEH,WAAW,CAACG,QAAS;IAC/BC,MAAM,EAAEJ,WAAW,CAACI,MAAO;IAC3B1C,OAAO,EAAEsC,WAAW,CAACtC,OAAQ;IAC7B2C,OAAO,EAAEZ;EAAuB,GAP3BO,WAAW,CAACN,EAAE;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAQpB,CACF;EAED,oBACElD,OAAA,CAACJ,sBAAsB;IACrBuD,IAAI,EAAE;MACJT,KAAK,EAAE5B,cAAc;MACrBsC,QAAQ,EAAE,mDAAmD;MAC7DC,IAAI,EAAEvD,cAAc,CAACwD,cAAc,CAACxC,cAAc,CAAC;MACnDyC,eAAe,EAAE;IACnB,CAAE;IACFjD,UAAU,EAAEA,UAAU,GAAG;MACvBkD,KAAK,EAAElD,UAAU,CAACmD,iBAAiB;MACnCC,MAAM,EAAEpD,UAAU,CAACqD,gBAAgB;MACnCC,QAAQ,EAAEtD,UAAU,CAACuD,kBAAkB;MACvCC,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,kBAAkB;MAC/BC,aAAa,EAAE;IACjB,CAAC,GAAGC,SAAU;IACdC,aAAa,EAAE;MACbC,IAAI,EAAE,WAAW;MACjBC,WAAW,EAAEtD,cAAc;MAC3BQ,KAAK,EAAE;IACT,CAAE;IAAA+C,QAAA,eAEFrE,OAAA,CAACH,uBAAuB;MACtByE,KAAK,EAAElE,YAAa;MACpBI,OAAO,EAAEA,OAAQ;MACjB+D,YAAY,EAAC,2DAA2D;MACxEC,SAAS,EAAC,cAAI;MACdC,UAAU,EAAEjC,qBAAsB;MAClCV,UAAU,EAAE;QACVpB,WAAW;QACXE,UAAU;QACV8D,YAAY,EAAEnC;MAChB;IAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACoB,CAAC;AAG7B,CAAC;AAAChD,EAAA,CA9GID,aAAuB;EAAA,QACPP,SAAS;AAAA;AAAAiF,EAAA,GADzB1E,aAAuB;AAgH7B,eAAeA,aAAa;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}