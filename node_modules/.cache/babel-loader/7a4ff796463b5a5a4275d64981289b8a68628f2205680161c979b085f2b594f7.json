{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx\",\n  _s = $RefreshSig$();\n/**\n * Enhanced Header Component\n * \n * Professional navigation header with dropdown menus,\n * mobile responsiveness, and industry-standard interactions.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Menu, X } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\nimport LanguageSwitcher from '../common/LanguageSwitcher';\nimport NavigationDropdown from '../navigation/NavigationDropdown';\nimport MobileNavigationDropdown from '../navigation/MobileNavigationDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedHeader = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const location = useLocation();\n  const {\n    translations\n  } = useLanguage();\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false);\n  }, [location.pathname]);\n\n  // Prevent body scroll when mobile menu is open\n  useEffect(() => {\n    if (isMobileMenuOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isMobileMenuOpen]);\n  const isActive = path => location.pathname === path;\n  const navigationItems = [{\n    path: '/',\n    label: translations.navigation.home,\n    icon: /*#__PURE__*/_jsxDEV(Home, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this),\n    exact: true\n  }, {\n    path: '/about',\n    label: translations.navigation.about,\n    icon: /*#__PURE__*/_jsxDEV(Info, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: '/guides',\n    label: translations.navigation.guides,\n    icon: /*#__PURE__*/_jsxDEV(Info, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this)\n  }, {\n    path: '/contact',\n    label: translations.navigation.contact,\n    icon: /*#__PURE__*/_jsxDEV(Phone, {\n      size: 16\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: `\n      fixed w-full top-0 z-50 transition-all duration-300 ease-in-out\n      ${isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100' : 'bg-white shadow-sm'}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/assets/images/MaBoursedetudeLogo.jpeg\",\n                alt: translations.brand.name,\n                className: \"h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight\",\n                children: translations.brand.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500 font-medium tracking-wider\",\n                children: translations.brand.tagline\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: `\n                flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n                transition-all duration-300 ease-in-out transform hover:scale-105\n                ${isActive('/') ? 'text-primary bg-primary/10 shadow-sm' : 'text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm'}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(Home, {\n              size: 16,\n              className: \"transition-colors duration-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"transition-colors duration-200\",\n              children: translations.navigation.home\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"countries\",\n            label: translations.navigation.countries\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"scholarships\",\n            label: translations.navigation.scholarships\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"opportunities\",\n            label: translations.navigation.opportunities\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), navigationItems.slice(1).map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `\n                  flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n                  transition-all duration-300 ease-in-out transform hover:scale-105\n                  ${isActive(item.path) ? 'text-primary bg-primary/10 shadow-sm' : 'text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm'}\n                `,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"transition-colors duration-200\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"transition-colors duration-200\",\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n              className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200\",\n              \"aria-expanded\": isMobileMenuOpen,\n              \"aria-label\": \"Toggle navigation menu\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-t border-gray-200 shadow-lg max-h-screen overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 pt-2 pb-3 space-y-1\",\n          children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            onClick: () => setIsMobileMenuOpen(false),\n            className: `\n                    flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium\n                    transition-colors duration-200 ease-in-out\n                    ${isActive(item.path) ? 'text-primary bg-primary/10 border-l-4 border-primary' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}\n                  `,\n            children: [item.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this)]\n          }, item.path, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-3 text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gray-50\",\n            children: \"Browse Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(MobileNavigationDropdown, {\n            type: \"countries\",\n            label: translations.navigation.countries,\n            onItemClick: () => setIsMobileMenuOpen(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(MobileNavigationDropdown, {\n            type: \"scholarships\",\n            label: translations.navigation.scholarships,\n            onItemClick: () => setIsMobileMenuOpen(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(MobileNavigationDropdown, {\n            type: \"opportunities\",\n            label: translations.navigation.opportunities,\n            onItemClick: () => setIsMobileMenuOpen(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedHeader, \"G0xTpqH9ESwWcM2u40uPD7FC/cg=\", false, function () {\n  return [useLocation, useLanguage];\n});\n_c = EnhancedHeader;\nexport default EnhancedHeader;\nvar _c;\n$RefreshReg$(_c, \"EnhancedHeader\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "<PERSON><PERSON>", "X", "useLanguage", "LanguageSwitcher", "NavigationDropdown", "MobileNavigationDropdown", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "isScrolled", "setIsScrolled", "location", "translations", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "pathname", "document", "body", "style", "overflow", "isActive", "path", "navigationItems", "label", "navigation", "home", "icon", "Home", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "exact", "about", "Info", "guides", "contact", "Phone", "className", "children", "to", "src", "alt", "brand", "name", "tagline", "type", "countries", "scholarships", "opportunities", "slice", "map", "item", "onClick", "onItemClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx"], "sourcesContent": ["/**\n * Enhanced Header Component\n * \n * Professional navigation header with dropdown menus,\n * mobile responsiveness, and industry-standard interactions.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Menu, X } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\nimport LanguageSwitcher from '../common/LanguageSwitcher';\nimport NavigationDropdown from '../navigation/NavigationDropdown';\nimport MobileNavigationDropdown from '../navigation/MobileNavigationDropdown';\n\nconst EnhancedHeader: React.FC = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const location = useLocation();\n  const { translations } = useLanguage();\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false);\n  }, [location.pathname]);\n\n  // Prevent body scroll when mobile menu is open\n  useEffect(() => {\n    if (isMobileMenuOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isMobileMenuOpen]);\n\n  const isActive = (path: string) => location.pathname === path;\n\n  const navigationItems = [\n    {\n      path: '/',\n      label: translations.navigation.home,\n      icon: <Home size={16} />,\n      exact: true\n    },\n    {\n      path: '/about',\n      label: translations.navigation.about,\n      icon: <Info size={16} />\n    },\n    {\n      path: '/guides',\n      label: translations.navigation.guides,\n      icon: <Info size={16} />\n    },\n    {\n      path: '/contact',\n      label: translations.navigation.contact,\n      icon: <Phone size={16} />\n    }\n  ];\n\n  return (\n    <header className={`\n      fixed w-full top-0 z-50 transition-all duration-300 ease-in-out\n      ${isScrolled \n        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100' \n        : 'bg-white shadow-sm'\n      }\n    `}>\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n              <div className=\"relative\">\n                <img\n                  src=\"/assets/images/MaBoursedetudeLogo.jpeg\"\n                  alt={translations.brand.name}\n                  className=\"h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              </div>\n              <div className=\"flex flex-col\">\n                <span className=\"text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight\">\n                  {translations.brand.name}\n                </span>\n                <span className=\"text-xs text-gray-500 font-medium tracking-wider\">\n                  {translations.brand.tagline}\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {/* Home Link */}\n            <Link\n              to=\"/\"\n              className={`\n                flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n                transition-all duration-300 ease-in-out transform hover:scale-105\n                ${isActive('/')\n                  ? 'text-primary bg-primary/10 shadow-sm'\n                  : 'text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm'\n                }\n              `}\n            >\n              <Home size={16} className=\"transition-colors duration-200\" />\n              <span className=\"transition-colors duration-200\">{translations.navigation.home}</span>\n            </Link>\n\n            {/* Dropdown Menus */}\n            <NavigationDropdown\n              type=\"countries\"\n              label={translations.navigation.countries}\n            />\n            \n            <NavigationDropdown\n              type=\"scholarships\"\n              label={translations.navigation.scholarships}\n            />\n            \n            <NavigationDropdown\n              type=\"opportunities\"\n              label={translations.navigation.opportunities}\n            />\n\n            {/* Regular Navigation Items */}\n            {navigationItems.slice(1).map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                className={`\n                  flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium\n                  transition-all duration-300 ease-in-out transform hover:scale-105\n                  ${isActive(item.path)\n                    ? 'text-primary bg-primary/10 shadow-sm'\n                    : 'text-gray-700 hover:text-primary hover:bg-primary/5 hover:shadow-sm'\n                  }\n                `}\n              >\n                <span className=\"transition-colors duration-200\">{item.icon}</span>\n                <span className=\"transition-colors duration-200\">{item.label}</span>\n              </Link>\n            ))}\n          </div>\n\n          {/* Right Side - Language Switcher & Mobile Menu */}\n          <div className=\"flex items-center space-x-4\">\n            <LanguageSwitcher />\n            \n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200\"\n                aria-expanded={isMobileMenuOpen}\n                aria-label=\"Toggle navigation menu\"\n              >\n                {isMobileMenuOpen ? (\n                  <X className=\"h-6 w-6\" />\n                ) : (\n                  <Menu className=\"h-6 w-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile Navigation Menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"bg-white border-t border-gray-200 shadow-lg max-h-screen overflow-y-auto\">\n            {/* Mobile Navigation Items */}\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`\n                    flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium\n                    transition-colors duration-200 ease-in-out\n                    ${isActive(item.path)\n                      ? 'text-primary bg-primary/10 border-l-4 border-primary'\n                      : 'text-gray-700 hover:text-primary hover:bg-gray-50'\n                    }\n                  `}\n                >\n                  {item.icon}\n                  <span>{item.label}</span>\n                </Link>\n              ))}\n            </div>\n\n            {/* Mobile Dropdown Sections */}\n            <div className=\"border-t border-gray-200\">\n              <div className=\"px-4 py-3 text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gray-50\">\n                Browse Categories\n              </div>\n\n              <MobileNavigationDropdown\n                type=\"countries\"\n                label={translations.navigation.countries}\n                onItemClick={() => setIsMobileMenuOpen(false)}\n              />\n\n              <MobileNavigationDropdown\n                type=\"scholarships\"\n                label={translations.navigation.scholarships}\n                onItemClick={() => setIsMobileMenuOpen(false)}\n              />\n\n              <MobileNavigationDropdown\n                type=\"opportunities\"\n                label={translations.navigation.opportunities}\n                onItemClick={() => setIsMobileMenuOpen(false)}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default EnhancedHeader;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AACtC,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAOC,gBAAgB,MAAM,4BAA4B;AACzD,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,wBAAwB,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMkB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAa,CAAC,GAAGb,WAAW,CAAC,CAAC;;EAEtC;EACAL,SAAS,CAAC,MAAM;IACd,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzBH,aAAa,CAACI,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnB,SAAS,CAAC,MAAM;IACdc,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACG,QAAQ,CAACO,QAAQ,CAAC,CAAC;;EAEvB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAIa,gBAAgB,EAAE;MACpBY,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACf,gBAAgB,CAAC,CAAC;EAEtB,MAAMgB,QAAQ,GAAIC,IAAY,IAAKb,QAAQ,CAACO,QAAQ,KAAKM,IAAI;EAE7D,MAAMC,eAAe,GAAG,CACtB;IACED,IAAI,EAAE,GAAG;IACTE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACC,IAAI;IACnCC,IAAI,eAAEzB,OAAA,CAAC0B,IAAI;MAACC,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEZ,IAAI,EAAE,QAAQ;IACdE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACU,KAAK;IACpCR,IAAI,eAAEzB,OAAA,CAACkC,IAAI;MAACP,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,SAAS;IACfE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACY,MAAM;IACrCV,IAAI,eAAEzB,OAAA,CAACkC,IAAI;MAACP,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,UAAU;IAChBE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACa,OAAO;IACtCX,IAAI,eAAEzB,OAAA,CAACqC,KAAK;MAACV,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC1B,CAAC,CACF;EAED,oBACE/B,OAAA;IAAQsC,SAAS,EAAE;AACvB;AACA,QAAQjC,UAAU,GACR,iEAAiE,GACjE,oBAAoB;AAC9B,KACM;IAAAkC,QAAA,gBACAvC,OAAA;MAAKsC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDvC,OAAA;QAAKsC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDvC,OAAA;UAAKsC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCvC,OAAA,CAACT,IAAI;YAACiD,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBACxDvC,OAAA;cAAKsC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBvC,OAAA;gBACEyC,GAAG,EAAC,wCAAwC;gBAC5CC,GAAG,EAAElC,YAAY,CAACmC,KAAK,CAACC,IAAK;gBAC7BN,SAAS,EAAC;cAAoG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,eACF/B,OAAA;gBAAKsC,SAAS,EAAC;cAAgJ;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC,eACN/B,OAAA;cAAKsC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BvC,OAAA;gBAAMsC,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,EACzH/B,YAAY,CAACmC,KAAK,CAACC;cAAI;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACP/B,OAAA;gBAAMsC,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAC/D/B,YAAY,CAACmC,KAAK,CAACE;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN/B,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpDvC,OAAA,CAACT,IAAI;YACHiD,EAAE,EAAC,GAAG;YACNF,SAAS,EAAE;AACzB;AACA;AACA,kBAAkBnB,QAAQ,CAAC,GAAG,CAAC,GACX,sCAAsC,GACtC,qEAAqE;AACzF,eACgB;YAAAoB,QAAA,gBAEFvC,OAAA,CAAC0B,IAAI;cAACC,IAAI,EAAE,EAAG;cAACW,SAAS,EAAC;YAAgC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7D/B,OAAA;cAAMsC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAE/B,YAAY,CAACe,UAAU,CAACC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eAGP/B,OAAA,CAACH,kBAAkB;YACjBiD,IAAI,EAAC,WAAW;YAChBxB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACwB;UAAU;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAEF/B,OAAA,CAACH,kBAAkB;YACjBiD,IAAI,EAAC,cAAc;YACnBxB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACyB;UAAa;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAEF/B,OAAA,CAACH,kBAAkB;YACjBiD,IAAI,EAAC,eAAe;YACpBxB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAAC0B;UAAc;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EAGDV,eAAe,CAAC6B,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,IAAI,iBACjCpD,OAAA,CAACT,IAAI;YAEHiD,EAAE,EAAEY,IAAI,CAAChC,IAAK;YACdkB,SAAS,EAAE;AAC3B;AACA;AACA,oBAAoBnB,QAAQ,CAACiC,IAAI,CAAChC,IAAI,CAAC,GACjB,sCAAsC,GACtC,qEAAqE;AAC3F,iBACkB;YAAAmB,QAAA,gBAEFvC,OAAA;cAAMsC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAEa,IAAI,CAAC3B;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnE/B,OAAA;cAAMsC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAEa,IAAI,CAAC9B;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAZ/DqB,IAAI,CAAChC,IAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/B,OAAA;UAAKsC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CvC,OAAA,CAACJ,gBAAgB;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGpB/B,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBvC,OAAA;cACEqD,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;cACtDmC,SAAS,EAAC,6MAA6M;cACvN,iBAAenC,gBAAiB;cAChC,cAAW,wBAAwB;cAAAoC,QAAA,EAElCpC,gBAAgB,gBACfH,OAAA,CAACN,CAAC;gBAAC4C,SAAS,EAAC;cAAS;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEzB/B,OAAA,CAACP,IAAI;gBAAC6C,SAAS,EAAC;cAAS;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC5B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5B,gBAAgB,iBACfH,OAAA;MAAKsC,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvC,OAAA;QAAKsC,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBAEvFvC,OAAA;UAAKsC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACtClB,eAAe,CAAC8B,GAAG,CAAEC,IAAI,iBACxBpD,OAAA,CAACT,IAAI;YAEHiD,EAAE,EAAEY,IAAI,CAAChC,IAAK;YACdiC,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;YAC1CkC,SAAS,EAAE;AAC7B;AACA;AACA,sBAAsBnB,QAAQ,CAACiC,IAAI,CAAChC,IAAI,CAAC,GACjB,sDAAsD,GACtD,mDAAmD;AAC3E,mBACoB;YAAAmB,QAAA,GAEDa,IAAI,CAAC3B,IAAI,eACVzB,OAAA;cAAAuC,QAAA,EAAOa,IAAI,CAAC9B;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAbpBqB,IAAI,CAAChC,IAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/B,OAAA;UAAKsC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCvC,OAAA;YAAKsC,SAAS,EAAC,mFAAmF;YAAAC,QAAA,EAAC;UAEnG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAEN/B,OAAA,CAACF,wBAAwB;YACvBgD,IAAI,EAAC,WAAW;YAChBxB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACwB,SAAU;YACzCO,WAAW,EAAEA,CAAA,KAAMlD,mBAAmB,CAAC,KAAK;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAEF/B,OAAA,CAACF,wBAAwB;YACvBgD,IAAI,EAAC,cAAc;YACnBxB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACyB,YAAa;YAC5CM,WAAW,EAAEA,CAAA,KAAMlD,mBAAmB,CAAC,KAAK;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAEF/B,OAAA,CAACF,wBAAwB;YACvBgD,IAAI,EAAC,eAAe;YACpBxB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAAC0B,aAAc;YAC7CK,WAAW,EAAEA,CAAA,KAAMlD,mBAAmB,CAAC,KAAK;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAAC7B,EAAA,CAhOID,cAAwB;EAAA,QAGXT,WAAW,EACHG,WAAW;AAAA;AAAA4D,EAAA,GAJhCtD,cAAwB;AAkO9B,eAAeA,cAAc;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}