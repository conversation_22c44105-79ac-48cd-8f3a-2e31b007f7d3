{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v5\",\n  key: \"nd4vlx\"\n}], [\"path\", {\n  d: \"M14.829 15.998a3 3 0 1 1-5.658 0\",\n  key: \"1pybiy\"\n}], [\"path\", {\n  d: \"M20.92 14.606A1 1 0 0 1 20 16H4a1 1 0 0 1-.92-1.394l3-7A1 1 0 0 1 7 7h10a1 1 0 0 1 .92.606z\",\n  key: \"ma1wor\"\n}]];\nconst LampCeiling = createLucideIcon(\"lamp-ceiling\", __iconNode);\nexport { __iconNode, LampCeiling as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/lamp-ceiling.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2v5', key: 'nd4vlx' }],\n  ['path', { d: 'M14.829 15.998a3 3 0 1 1-5.658 0', key: '1pybiy' }],\n  [\n    'path',\n    {\n      d: 'M20.92 14.606A1 1 0 0 1 20 16H4a1 1 0 0 1-.92-1.394l3-7A1 1 0 0 1 7 7h10a1 1 0 0 1 .92.606z',\n      key: 'ma1wor',\n    },\n  ],\n];\n\n/**\n * @component @name LampCeiling\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnY1IiAvPgogIDxwYXRoIGQ9Ik0xNC44MjkgMTUuOTk4YTMgMyAwIDEgMS01LjY1OCAwIiAvPgogIDxwYXRoIGQ9Ik0yMC45MiAxNC42MDZBMSAxIDAgMCAxIDIwIDE2SDRhMSAxIDAgMCAxLS45Mi0xLjM5NGwzLTdBMSAxIDAgMCAxIDcgN2gxMGExIDEgMCAwIDEgLjkyLjYwNnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/lamp-ceiling\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LampCeiling = createLucideIcon('lamp-ceiling', __iconNode);\n\nexport default LampCeiling;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,WAAA,GAAcC,gBAAiB,iBAAgBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}