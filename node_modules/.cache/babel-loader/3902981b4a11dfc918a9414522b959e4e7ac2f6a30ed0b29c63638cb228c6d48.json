{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx\",\n  _s = $RefreshSig$();\n/**\n * Enhanced Header Component\n * \n * Professional navigation header with dropdown menus,\n * mobile responsiveness, and industry-standard interactions.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Menu, X } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\nimport LanguageSwitcher from '../common/LanguageSwitcher';\nimport NavigationDropdown from '../navigation/NavigationDropdown';\nimport MobileNavigationDropdown from '../navigation/MobileNavigationDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedHeader = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const location = useLocation();\n  const {\n    translations\n  } = useLanguage();\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false);\n  }, [location.pathname]);\n\n  // Prevent body scroll when mobile menu is open\n  useEffect(() => {\n    if (isMobileMenuOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isMobileMenuOpen]);\n  const isActive = path => location.pathname === path;\n  const navigationItems = [{\n    path: '/',\n    label: translations.navigation.home,\n    exact: true\n  }, {\n    path: '/about',\n    label: translations.navigation.about\n  }, {\n    path: '/guides',\n    label: translations.navigation.guides\n  }, {\n    path: '/contact',\n    label: translations.navigation.contact\n  }];\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: `\n      fixed w-full top-0 z-50 transition-all duration-300 ease-in-out\n      ${isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100' : 'bg-white shadow-sm'}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"flex items-center space-x-3 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/assets/images/MaBoursedetudeLogo.jpeg\",\n                alt: translations.brand.name,\n                className: \"h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight\",\n                children: translations.brand.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500 font-medium tracking-wider\",\n                children: translations.brand.tagline\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: `\n                px-3 py-2 rounded-md text-sm font-medium\n                transition-all duration-200 ease-in-out\n                ${isActive('/') ? 'text-primary bg-primary/10' : 'text-gray-700 hover:text-primary hover:bg-primary/5'}\n              `,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"transition-colors duration-200\",\n              children: translations.navigation.home\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"countries\",\n            label: translations.navigation.countries\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"scholarships\",\n            label: translations.navigation.scholarships\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NavigationDropdown, {\n            type: \"opportunities\",\n            label: translations.navigation.opportunities\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), navigationItems.slice(1).map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.path,\n            className: `\n                  px-3 py-2 rounded-md text-sm font-medium\n                  transition-all duration-200 ease-in-out\n                  ${isActive(item.path) ? 'text-primary bg-primary/10' : 'text-gray-700 hover:text-primary hover:bg-primary/5'}\n                `,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"transition-colors duration-200\",\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)\n          }, item.path, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n              className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200\",\n              \"aria-expanded\": isMobileMenuOpen,\n              \"aria-label\": \"Toggle navigation menu\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white border-t border-gray-200 shadow-lg max-h-screen overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            onClick: () => setIsMobileMenuOpen(false),\n            className: `\n                  block px-4 py-3 text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive('/') ? 'text-primary bg-primary/10 border-l-4 border-primary' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}\n                `,\n            children: translations.navigation.home\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(MobileNavigationDropdown, {\n            type: \"countries\",\n            label: translations.navigation.countries,\n            onItemClick: () => setIsMobileMenuOpen(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(MobileNavigationDropdown, {\n            type: \"scholarships\",\n            label: translations.navigation.scholarships,\n            onItemClick: () => setIsMobileMenuOpen(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(MobileNavigationDropdown, {\n            type: \"opportunities\",\n            label: translations.navigation.opportunities,\n            onItemClick: () => setIsMobileMenuOpen(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/guides\",\n            onClick: () => setIsMobileMenuOpen(false),\n            className: `\n                  block px-4 py-3 text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive('/guides') ? 'text-primary bg-primary/10 border-l-4 border-primary' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}\n                `,\n            children: translations.navigation.guides\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            onClick: () => setIsMobileMenuOpen(false),\n            className: `\n                  block px-4 py-3 text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive('/about') ? 'text-primary bg-primary/10 border-l-4 border-primary' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}\n                `,\n            children: translations.navigation.about\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            onClick: () => setIsMobileMenuOpen(false),\n            className: `\n                  block px-4 py-3 text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive('/contact') ? 'text-primary bg-primary/10 border-l-4 border-primary' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}\n                `,\n            children: translations.navigation.contact\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedHeader, \"G0xTpqH9ESwWcM2u40uPD7FC/cg=\", false, function () {\n  return [useLocation, useLanguage];\n});\n_c = EnhancedHeader;\nexport default EnhancedHeader;\nvar _c;\n$RefreshReg$(_c, \"EnhancedHeader\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "<PERSON><PERSON>", "X", "useLanguage", "LanguageSwitcher", "NavigationDropdown", "MobileNavigationDropdown", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "isScrolled", "setIsScrolled", "location", "translations", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "pathname", "document", "body", "style", "overflow", "isActive", "path", "navigationItems", "label", "navigation", "home", "exact", "about", "guides", "contact", "className", "children", "to", "src", "alt", "brand", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tagline", "type", "countries", "scholarships", "opportunities", "slice", "map", "item", "onClick", "onItemClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx"], "sourcesContent": ["/**\n * Enhanced Header Component\n * \n * Professional navigation header with dropdown menus,\n * mobile responsiveness, and industry-standard interactions.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { Menu, X } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\nimport LanguageSwitcher from '../common/LanguageSwitcher';\nimport NavigationDropdown from '../navigation/NavigationDropdown';\nimport MobileNavigationDropdown from '../navigation/MobileNavigationDropdown';\n\nconst EnhancedHeader: React.FC = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const location = useLocation();\n  const { translations } = useLanguage();\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false);\n  }, [location.pathname]);\n\n  // Prevent body scroll when mobile menu is open\n  useEffect(() => {\n    if (isMobileMenuOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isMobileMenuOpen]);\n\n  const isActive = (path: string) => location.pathname === path;\n\n  const navigationItems = [\n    {\n      path: '/',\n      label: translations.navigation.home,\n      exact: true\n    },\n    {\n      path: '/about',\n      label: translations.navigation.about\n    },\n    {\n      path: '/guides',\n      label: translations.navigation.guides\n    },\n    {\n      path: '/contact',\n      label: translations.navigation.contact\n    }\n  ];\n\n  return (\n    <header className={`\n      fixed w-full top-0 z-50 transition-all duration-300 ease-in-out\n      ${isScrolled \n        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100' \n        : 'bg-white shadow-sm'\n      }\n    `}>\n      <nav className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex items-center space-x-3 group\">\n              <div className=\"relative\">\n                <img\n                  src=\"/assets/images/MaBoursedetudeLogo.jpeg\"\n                  alt={translations.brand.name}\n                  className=\"h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              </div>\n              <div className=\"flex flex-col\">\n                <span className=\"text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight\">\n                  {translations.brand.name}\n                </span>\n                <span className=\"text-xs text-gray-500 font-medium tracking-wider\">\n                  {translations.brand.tagline}\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {/* Home Link */}\n            <Link\n              to=\"/\"\n              className={`\n                px-3 py-2 rounded-md text-sm font-medium\n                transition-all duration-200 ease-in-out\n                ${isActive('/')\n                  ? 'text-primary bg-primary/10'\n                  : 'text-gray-700 hover:text-primary hover:bg-primary/5'\n                }\n              `}\n            >\n              <span className=\"transition-colors duration-200\">{translations.navigation.home}</span>\n            </Link>\n\n            {/* Dropdown Menus */}\n            <NavigationDropdown\n              type=\"countries\"\n              label={translations.navigation.countries}\n            />\n            \n            <NavigationDropdown\n              type=\"scholarships\"\n              label={translations.navigation.scholarships}\n            />\n            \n            <NavigationDropdown\n              type=\"opportunities\"\n              label={translations.navigation.opportunities}\n            />\n\n            {/* Regular Navigation Items */}\n            {navigationItems.slice(1).map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                className={`\n                  px-3 py-2 rounded-md text-sm font-medium\n                  transition-all duration-200 ease-in-out\n                  ${isActive(item.path)\n                    ? 'text-primary bg-primary/10'\n                    : 'text-gray-700 hover:text-primary hover:bg-primary/5'\n                  }\n                `}\n              >\n                <span className=\"transition-colors duration-200\">{item.label}</span>\n              </Link>\n            ))}\n          </div>\n\n          {/* Right Side - Language Switcher & Mobile Menu */}\n          <div className=\"flex items-center space-x-4\">\n            <LanguageSwitcher />\n            \n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200\"\n                aria-expanded={isMobileMenuOpen}\n                aria-label=\"Toggle navigation menu\"\n              >\n                {isMobileMenuOpen ? (\n                  <X className=\"h-6 w-6\" />\n                ) : (\n                  <Menu className=\"h-6 w-6\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile Navigation Menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"bg-white border-t border-gray-200 shadow-lg max-h-screen overflow-y-auto\">\n            {/* Mobile Navigation Items - Same order as desktop */}\n            <div className=\"py-2\">\n              {/* Home */}\n              <Link\n                to=\"/\"\n                onClick={() => setIsMobileMenuOpen(false)}\n                className={`\n                  block px-4 py-3 text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive('/')\n                    ? 'text-primary bg-primary/10 border-l-4 border-primary'\n                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'\n                  }\n                `}\n              >\n                {translations.navigation.home}\n              </Link>\n\n              {/* Countries with dropdown */}\n              <MobileNavigationDropdown\n                type=\"countries\"\n                label={translations.navigation.countries}\n                onItemClick={() => setIsMobileMenuOpen(false)}\n              />\n\n              {/* Scholarships with dropdown */}\n              <MobileNavigationDropdown\n                type=\"scholarships\"\n                label={translations.navigation.scholarships}\n                onItemClick={() => setIsMobileMenuOpen(false)}\n              />\n\n              {/* Opportunities with dropdown */}\n              <MobileNavigationDropdown\n                type=\"opportunities\"\n                label={translations.navigation.opportunities}\n                onItemClick={() => setIsMobileMenuOpen(false)}\n              />\n\n              {/* Guides */}\n              <Link\n                to=\"/guides\"\n                onClick={() => setIsMobileMenuOpen(false)}\n                className={`\n                  block px-4 py-3 text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive('/guides')\n                    ? 'text-primary bg-primary/10 border-l-4 border-primary'\n                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'\n                  }\n                `}\n              >\n                {translations.navigation.guides}\n              </Link>\n\n              {/* About */}\n              <Link\n                to=\"/about\"\n                onClick={() => setIsMobileMenuOpen(false)}\n                className={`\n                  block px-4 py-3 text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive('/about')\n                    ? 'text-primary bg-primary/10 border-l-4 border-primary'\n                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'\n                  }\n                `}\n              >\n                {translations.navigation.about}\n              </Link>\n\n              {/* Contact */}\n              <Link\n                to=\"/contact\"\n                onClick={() => setIsMobileMenuOpen(false)}\n                className={`\n                  block px-4 py-3 text-base font-medium\n                  transition-colors duration-200 ease-in-out\n                  ${isActive('/contact')\n                    ? 'text-primary bg-primary/10 border-l-4 border-primary'\n                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'\n                  }\n                `}\n              >\n                {translations.navigation.contact}\n              </Link>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default EnhancedHeader;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AACtC,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAOC,gBAAgB,MAAM,4BAA4B;AACzD,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,wBAAwB,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMkB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAa,CAAC,GAAGb,WAAW,CAAC,CAAC;;EAEtC;EACAL,SAAS,CAAC,MAAM;IACd,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzBH,aAAa,CAACI,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnB,SAAS,CAAC,MAAM;IACdc,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACG,QAAQ,CAACO,QAAQ,CAAC,CAAC;;EAEvB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAIa,gBAAgB,EAAE;MACpBY,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACf,gBAAgB,CAAC,CAAC;EAEtB,MAAMgB,QAAQ,GAAIC,IAAY,IAAKb,QAAQ,CAACO,QAAQ,KAAKM,IAAI;EAE7D,MAAMC,eAAe,GAAG,CACtB;IACED,IAAI,EAAE,GAAG;IACTE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACC,IAAI;IACnCC,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,QAAQ;IACdE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACG;EACjC,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACI;EACjC,CAAC,EACD;IACEP,IAAI,EAAE,UAAU;IAChBE,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACK;EACjC,CAAC,CACF;EAED,oBACE5B,OAAA;IAAQ6B,SAAS,EAAE;AACvB;AACA,QAAQxB,UAAU,GACR,iEAAiE,GACjE,oBAAoB;AAC9B,KACM;IAAAyB,QAAA,gBACA9B,OAAA;MAAK6B,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrD9B,OAAA;QAAK6B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErD9B,OAAA;UAAK6B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC9B,OAAA,CAACT,IAAI;YAACwC,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBACxD9B,OAAA;cAAK6B,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB9B,OAAA;gBACEgC,GAAG,EAAC,wCAAwC;gBAC5CC,GAAG,EAAEzB,YAAY,CAAC0B,KAAK,CAACC,IAAK;gBAC7BN,SAAS,EAAC;cAAoG;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,eACFvC,OAAA;gBAAK6B,SAAS,EAAC;cAAgJ;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnK,CAAC,eACNvC,OAAA;cAAK6B,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B9B,OAAA;gBAAM6B,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,EACzHtB,YAAY,CAAC0B,KAAK,CAACC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACPvC,OAAA;gBAAM6B,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,EAC/DtB,YAAY,CAAC0B,KAAK,CAACM;cAAO;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNvC,OAAA;UAAK6B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpD9B,OAAA,CAACT,IAAI;YACHwC,EAAE,EAAC,GAAG;YACNF,SAAS,EAAE;AACzB;AACA;AACA,kBAAkBV,QAAQ,CAAC,GAAG,CAAC,GACX,4BAA4B,GAC5B,qDAAqD;AACzE,eACgB;YAAAW,QAAA,eAEF9B,OAAA;cAAM6B,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAEtB,YAAY,CAACe,UAAU,CAACC;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eAGPvC,OAAA,CAACH,kBAAkB;YACjB4C,IAAI,EAAC,WAAW;YAChBnB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACmB;UAAU;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAEFvC,OAAA,CAACH,kBAAkB;YACjB4C,IAAI,EAAC,cAAc;YACnBnB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACoB;UAAa;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAEFvC,OAAA,CAACH,kBAAkB;YACjB4C,IAAI,EAAC,eAAe;YACpBnB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACqB;UAAc;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EAGDlB,eAAe,CAACwB,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAAEC,IAAI,iBACjC/C,OAAA,CAACT,IAAI;YAEHwC,EAAE,EAAEgB,IAAI,CAAC3B,IAAK;YACdS,SAAS,EAAE;AAC3B;AACA;AACA,oBAAoBV,QAAQ,CAAC4B,IAAI,CAAC3B,IAAI,CAAC,GACjB,4BAA4B,GAC5B,qDAAqD;AAC3E,iBACkB;YAAAU,QAAA,eAEF9B,OAAA;cAAM6B,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAEiB,IAAI,CAACzB;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC,GAX/DQ,IAAI,CAAC3B,IAAI;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYV,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNvC,OAAA;UAAK6B,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C9B,OAAA,CAACJ,gBAAgB;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGpBvC,OAAA;YAAK6B,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB9B,OAAA;cACEgD,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;cACtD0B,SAAS,EAAC,6MAA6M;cACvN,iBAAe1B,gBAAiB;cAChC,cAAW,wBAAwB;cAAA2B,QAAA,EAElC3B,gBAAgB,gBACfH,OAAA,CAACN,CAAC;gBAACmC,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEzBvC,OAAA,CAACP,IAAI;gBAACoC,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC5B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpC,gBAAgB,iBACfH,OAAA;MAAK6B,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB9B,OAAA;QAAK6B,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eAEvF9B,OAAA;UAAK6B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAEnB9B,OAAA,CAACT,IAAI;YACHwC,EAAE,EAAC,GAAG;YACNiB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;YAC1CyB,SAAS,EAAE;AAC3B;AACA;AACA,oBAAoBV,QAAQ,CAAC,GAAG,CAAC,GACX,sDAAsD,GACtD,mDAAmD;AACzE,iBACkB;YAAAW,QAAA,EAEDtB,YAAY,CAACe,UAAU,CAACC;UAAI;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eAGPvC,OAAA,CAACF,wBAAwB;YACvB2C,IAAI,EAAC,WAAW;YAChBnB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACmB,SAAU;YACzCO,WAAW,EAAEA,CAAA,KAAM7C,mBAAmB,CAAC,KAAK;UAAE;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAGFvC,OAAA,CAACF,wBAAwB;YACvB2C,IAAI,EAAC,cAAc;YACnBnB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACoB,YAAa;YAC5CM,WAAW,EAAEA,CAAA,KAAM7C,mBAAmB,CAAC,KAAK;UAAE;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAGFvC,OAAA,CAACF,wBAAwB;YACvB2C,IAAI,EAAC,eAAe;YACpBnB,KAAK,EAAEd,YAAY,CAACe,UAAU,CAACqB,aAAc;YAC7CK,WAAW,EAAEA,CAAA,KAAM7C,mBAAmB,CAAC,KAAK;UAAE;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAGFvC,OAAA,CAACT,IAAI;YACHwC,EAAE,EAAC,SAAS;YACZiB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;YAC1CyB,SAAS,EAAE;AAC3B;AACA;AACA,oBAAoBV,QAAQ,CAAC,SAAS,CAAC,GACjB,sDAAsD,GACtD,mDAAmD;AACzE,iBACkB;YAAAW,QAAA,EAEDtB,YAAY,CAACe,UAAU,CAACI;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGPvC,OAAA,CAACT,IAAI;YACHwC,EAAE,EAAC,QAAQ;YACXiB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;YAC1CyB,SAAS,EAAE;AAC3B;AACA;AACA,oBAAoBV,QAAQ,CAAC,QAAQ,CAAC,GAChB,sDAAsD,GACtD,mDAAmD;AACzE,iBACkB;YAAAW,QAAA,EAEDtB,YAAY,CAACe,UAAU,CAACG;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAGPvC,OAAA,CAACT,IAAI;YACHwC,EAAE,EAAC,UAAU;YACbiB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;YAC1CyB,SAAS,EAAE;AAC3B;AACA;AACA,oBAAoBV,QAAQ,CAAC,UAAU,CAAC,GAClB,sDAAsD,GACtD,mDAAmD;AACzE,iBACkB;YAAAW,QAAA,EAEDtB,YAAY,CAACe,UAAU,CAACK;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACrC,EAAA,CAnQID,cAAwB;EAAA,QAGXT,WAAW,EACHG,WAAW;AAAA;AAAAuD,EAAA,GAJhCjD,cAAwB;AAqQ9B,eAAeA,cAAc;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}