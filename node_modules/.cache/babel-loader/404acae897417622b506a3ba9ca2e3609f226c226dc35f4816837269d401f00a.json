{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx\",\n  _s = $RefreshSig$();\n/**\n * Mobile Navigation Dropdown Component\n * \n * Touch-friendly accordion-style dropdown for mobile navigation\n * with smooth animations and proper accessibility.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobileNavigationDropdown = ({\n  type,\n  label,\n  onItemClick\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [items, setItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    translations\n  } = useLanguage();\n  const fetchData = async () => {\n    if (items.length > 0) return;\n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor = () => [];\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = countries => [{\n            id: 'all-countries',\n            label: translations.navigation.allCountries || 'All Countries',\n            href: '/countries',\n            count: countries.reduce((sum, c) => sum + c.count, 0)\n          }, ...countries.slice(0, 6).map(country => ({\n            id: country.slug,\n            label: country.name,\n            href: `/countries/${encodeURIComponent(country.name)}`,\n            count: country.count\n          }))];\n          break;\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = levels => [{\n            id: 'all-scholarships',\n            label: translations.navigation.allScholarships || 'All Scholarships',\n            href: '/scholarships',\n            count: levels.reduce((sum, l) => sum + l.count, 0)\n          }, ...levels.map(level => ({\n            id: level.slug,\n            label: level.name,\n            href: `/scholarships?level=${encodeURIComponent(level.name)}`,\n            count: level.openCount\n          }))];\n          break;\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = types => [{\n            id: 'all-opportunities',\n            label: translations.navigation.allOpportunities || 'All Opportunities',\n            href: '/opportunities',\n            count: types.reduce((sum, t) => sum + t.count, 0)\n          }, ...types.map(opType => ({\n            id: opType.slug,\n            label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n            href: `/opportunities?type=${encodeURIComponent(opType.name)}`,\n            count: opType.activeCount\n          }))];\n          break;\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      const result = await response.json();\n      const data = result.data || result;\n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        href: `/${type}`\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleToggle = () => {\n    if (!isOpen && items.length === 0) {\n      fetchData();\n    }\n    setIsOpen(!isOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"border-b border-gray-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleToggle,\n      className: \"w-full flex items-center justify-between px-4 py-3 text-left text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 transition-colors duration-200\",\n      \"aria-expanded\": isOpen,\n      \"aria-controls\": `mobile-dropdown-${type}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 18,\n        className: `text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      id: `mobile-dropdown-${type}`,\n      className: `\n          overflow-hidden transition-all duration-300 ease-in-out\n          ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}\n        `,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 border-t border-gray-200\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-primary mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this) : items.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 text-center text-gray-500 text-sm\",\n          children: \"No items available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2\",\n          children: items.map(item => /*#__PURE__*/_jsxDEV(Link, {\n            to: item.href,\n            onClick: onItemClick,\n            className: \"flex items-center justify-between px-6 py-3 text-sm text-gray-600 hover:text-primary hover:bg-white transition-colors duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 19\n            }, this), item.count !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\",\n              children: item.count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 21\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(MobileNavigationDropdown, \"cB8is+q46C6RbO8OO77S0MTBqWQ=\", false, function () {\n  return [useLanguage];\n});\n_c = MobileNavigationDropdown;\nexport default MobileNavigationDropdown;\nvar _c;\n$RefreshReg$(_c, \"MobileNavigationDropdown\");", "map": {"version": 3, "names": ["React", "useState", "Link", "ChevronDown", "useLanguage", "jsxDEV", "_jsxDEV", "MobileNavigationDropdown", "type", "label", "onItemClick", "_s", "isOpen", "setIsOpen", "items", "setItems", "loading", "setLoading", "translations", "fetchData", "length", "endpoint", "dataProcessor", "countries", "id", "navigation", "allCountries", "href", "count", "reduce", "sum", "c", "slice", "map", "country", "slug", "name", "encodeURIComponent", "levels", "allScholarships", "l", "level", "openCount", "types", "allOpportunities", "t", "opType", "char<PERSON>t", "toUpperCase", "activeCount", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "result", "json", "data", "error", "console", "handleToggle", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "item", "to", "undefined", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx"], "sourcesContent": ["/**\n * Mobile Navigation Dropdown Component\n * \n * Touch-friendly accordion-style dropdown for mobile navigation\n * with smooth animations and proper accessibility.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport { useLanguage } from '../../context/LanguageContext';\n\ninterface MobileNavigationDropdownProps {\n  type: 'countries' | 'scholarships' | 'opportunities';\n  label: string;\n  onItemClick?: () => void;\n}\n\ninterface CountryData {\n  name: string;\n  count: number;\n  slug: string;\n}\n\ninterface LevelData {\n  name: string;\n  count: number;\n  openCount: number;\n  slug: string;\n}\n\ninterface OpportunityTypeData {\n  name: string;\n  count: number;\n  activeCount: number;\n  slug: string;\n}\n\nconst MobileNavigationDropdown: React.FC<MobileNavigationDropdownProps> = ({\n  type,\n  label,\n  onItemClick\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [items, setItems] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const { translations } = useLanguage();\n\n  const fetchData = async () => {\n    if (items.length > 0) return;\n    \n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor: (data: any[]) => any[] = () => [];\n\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = (countries: CountryData[]) => [\n            {\n              id: 'all-countries',\n              label: translations.navigation.allCountries || 'All Countries',\n              href: '/countries',\n              count: countries.reduce((sum, c) => sum + c.count, 0)\n            },\n            ...countries.slice(0, 6).map(country => ({\n              id: country.slug,\n              label: country.name,\n              href: `/countries/${encodeURIComponent(country.name)}`,\n              count: country.count\n            }))\n          ];\n          break;\n\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = (levels: LevelData[]) => [\n            {\n              id: 'all-scholarships',\n              label: translations.navigation.allScholarships || 'All Scholarships',\n              href: '/scholarships',\n              count: levels.reduce((sum, l) => sum + l.count, 0)\n            },\n            ...levels.map(level => ({\n              id: level.slug,\n              label: level.name,\n              href: `/scholarships?level=${encodeURIComponent(level.name)}`,\n              count: level.openCount\n            }))\n          ];\n          break;\n\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = (types: OpportunityTypeData[]) => [\n            {\n              id: 'all-opportunities',\n              label: translations.navigation.allOpportunities || 'All Opportunities',\n              href: '/opportunities',\n              count: types.reduce((sum, t) => sum + t.count, 0)\n            },\n            ...types.map(opType => ({\n              id: opType.slug,\n              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n              href: `/opportunities?type=${encodeURIComponent(opType.name)}`,\n              count: opType.activeCount\n            }))\n          ];\n          break;\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      \n      const result = await response.json();\n      const data = result.data || result;\n      \n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        href: `/${type}`\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleToggle = () => {\n    if (!isOpen && items.length === 0) {\n      fetchData();\n    }\n    setIsOpen(!isOpen);\n  };\n\n\n\n  return (\n    <div className=\"border-b border-gray-200\">\n      {/* Main Toggle Button */}\n      <button\n        onClick={handleToggle}\n        className=\"w-full flex items-center justify-between px-4 py-3 text-left text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 transition-colors duration-200\"\n        aria-expanded={isOpen}\n        aria-controls={`mobile-dropdown-${type}`}\n      >\n        <span>{label}</span>\n        <ChevronDown\n          size={18}\n          className={`text-gray-400 transition-transform duration-200 ${\n            isOpen ? 'rotate-180' : ''\n          }`}\n        />\n      </button>\n\n      {/* Dropdown Content */}\n      <div\n        id={`mobile-dropdown-${type}`}\n        className={`\n          overflow-hidden transition-all duration-300 ease-in-out\n          ${isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}\n        `}\n      >\n        <div className=\"bg-gray-50 border-t border-gray-200\">\n          {loading ? (\n            <div className=\"px-6 py-4 text-center\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-primary mx-auto mb-2\"></div>\n              <span className=\"text-sm text-gray-500\">Loading...</span>\n            </div>\n          ) : items.length === 0 ? (\n            <div className=\"px-6 py-4 text-center text-gray-500 text-sm\">\n              No items available\n            </div>\n          ) : (\n            <div className=\"py-2\">\n              {items.map((item) => (\n                <Link\n                  key={item.id}\n                  to={item.href}\n                  onClick={onItemClick}\n                  className=\"flex items-center justify-between px-6 py-3 text-sm text-gray-600 hover:text-primary hover:bg-white transition-colors duration-200\"\n                >\n                  <span>{item.label}</span>\n                  {item.count !== undefined && (\n                    <span className=\"text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full\">\n                      {item.count}\n                    </span>\n                  )}\n                </Link>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MobileNavigationDropdown;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4B5D,MAAMC,wBAAiE,GAAGA,CAAC;EACzEC,IAAI;EACJC,KAAK;EACLC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAQ,EAAE,CAAC;EAC7C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEiB;EAAa,CAAC,GAAGd,WAAW,CAAC,CAAC;EAEtC,MAAMe,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;IAEtBH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAII,QAAQ,GAAG,EAAE;MACjB,IAAIC,aAAqC,GAAGA,CAAA,KAAM,EAAE;MAEpD,QAAQd,IAAI;QACV,KAAK,WAAW;UACda,QAAQ,GAAG,gBAAgB;UAC3BC,aAAa,GAAIC,SAAwB,IAAK,CAC5C;YACEC,EAAE,EAAE,eAAe;YACnBf,KAAK,EAAES,YAAY,CAACO,UAAU,CAACC,YAAY,IAAI,eAAe;YAC9DC,IAAI,EAAE,YAAY;YAClBC,KAAK,EAAEL,SAAS,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACH,KAAK,EAAE,CAAC;UACtD,CAAC,EACD,GAAGL,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,OAAO,KAAK;YACvCV,EAAE,EAAEU,OAAO,CAACC,IAAI;YAChB1B,KAAK,EAAEyB,OAAO,CAACE,IAAI;YACnBT,IAAI,EAAE,cAAcU,kBAAkB,CAACH,OAAO,CAACE,IAAI,CAAC,EAAE;YACtDR,KAAK,EAAEM,OAAO,CAACN;UACjB,CAAC,CAAC,CAAC,CACJ;UACD;QAEF,KAAK,cAAc;UACjBP,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIgB,MAAmB,IAAK,CACvC;YACEd,EAAE,EAAE,kBAAkB;YACtBf,KAAK,EAAES,YAAY,CAACO,UAAU,CAACc,eAAe,IAAI,kBAAkB;YACpEZ,IAAI,EAAE,eAAe;YACrBC,KAAK,EAAEU,MAAM,CAACT,MAAM,CAAC,CAACC,GAAG,EAAEU,CAAC,KAAKV,GAAG,GAAGU,CAAC,CAACZ,KAAK,EAAE,CAAC;UACnD,CAAC,EACD,GAAGU,MAAM,CAACL,GAAG,CAACQ,KAAK,KAAK;YACtBjB,EAAE,EAAEiB,KAAK,CAACN,IAAI;YACd1B,KAAK,EAAEgC,KAAK,CAACL,IAAI;YACjBT,IAAI,EAAE,uBAAuBU,kBAAkB,CAACI,KAAK,CAACL,IAAI,CAAC,EAAE;YAC7DR,KAAK,EAAEa,KAAK,CAACC;UACf,CAAC,CAAC,CAAC,CACJ;UACD;QAEF,KAAK,eAAe;UAClBrB,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIqB,KAA4B,IAAK,CAChD;YACEnB,EAAE,EAAE,mBAAmB;YACvBf,KAAK,EAAES,YAAY,CAACO,UAAU,CAACmB,gBAAgB,IAAI,mBAAmB;YACtEjB,IAAI,EAAE,gBAAgB;YACtBC,KAAK,EAAEe,KAAK,CAACd,MAAM,CAAC,CAACC,GAAG,EAAEe,CAAC,KAAKf,GAAG,GAAGe,CAAC,CAACjB,KAAK,EAAE,CAAC;UAClD,CAAC,EACD,GAAGe,KAAK,CAACV,GAAG,CAACa,MAAM,KAAK;YACtBtB,EAAE,EAAEsB,MAAM,CAACX,IAAI;YACf1B,KAAK,EAAEqC,MAAM,CAACV,IAAI,CAACW,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACV,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC;YACjEL,IAAI,EAAE,uBAAuBU,kBAAkB,CAACS,MAAM,CAACV,IAAI,CAAC,EAAE;YAC9DR,KAAK,EAAEkB,MAAM,CAACG;UAChB,CAAC,CAAC,CAAC,CACJ;UACD;MACJ;MAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAGjC,QAAQ,EAAE,CAAC;MACtG,IAAI,CAAC6B,QAAQ,CAACK,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MAEzD,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM;MAElC1C,QAAQ,CAACO,aAAa,CAACqC,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkBpD,IAAI,QAAQ,EAAEoD,KAAK,CAAC;MACpD7C,QAAQ,CAAC,CAAC;QACRS,EAAE,EAAE,OAAO;QACXf,KAAK,EAAE,qBAAqB;QAC5BkB,IAAI,EAAE,IAAInB,IAAI;MAChB,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRS,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAClD,MAAM,IAAIE,KAAK,CAACM,MAAM,KAAK,CAAC,EAAE;MACjCD,SAAS,CAAC,CAAC;IACb;IACAN,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;EAID,oBACEN,OAAA;IAAKyD,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBAEvC1D,OAAA;MACE2D,OAAO,EAAEH,YAAa;MACtBC,SAAS,EAAC,qKAAqK;MAC/K,iBAAenD,MAAO;MACtB,iBAAe,mBAAmBJ,IAAI,EAAG;MAAAwD,QAAA,gBAEzC1D,OAAA;QAAA0D,QAAA,EAAOvD;MAAK;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpB/D,OAAA,CAACH,WAAW;QACVmE,IAAI,EAAE,EAAG;QACTP,SAAS,EAAE,mDACTnD,MAAM,GAAG,YAAY,GAAG,EAAE;MACzB;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGT/D,OAAA;MACEkB,EAAE,EAAE,mBAAmBhB,IAAI,EAAG;MAC9BuD,SAAS,EAAE;AACnB;AACA,YAAYnD,MAAM,GAAG,sBAAsB,GAAG,mBAAmB;AACjE,SAAU;MAAAoD,QAAA,eAEF1D,OAAA;QAAKyD,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EACjDhD,OAAO,gBACNV,OAAA;UAAKyD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC1D,OAAA;YAAKyD,SAAS,EAAC;UAA0E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChG/D,OAAA;YAAMyD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,GACJvD,KAAK,CAACM,MAAM,KAAK,CAAC,gBACpBd,OAAA;UAAKyD,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAE7D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEN/D,OAAA;UAAKyD,SAAS,EAAC,MAAM;UAAAC,QAAA,EAClBlD,KAAK,CAACmB,GAAG,CAAEsC,IAAI,iBACdjE,OAAA,CAACJ,IAAI;YAEHsE,EAAE,EAAED,IAAI,CAAC5C,IAAK;YACdsC,OAAO,EAAEvD,WAAY;YACrBqD,SAAS,EAAC,oIAAoI;YAAAC,QAAA,gBAE9I1D,OAAA;cAAA0D,QAAA,EAAOO,IAAI,CAAC9D;YAAK;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxBE,IAAI,CAAC3C,KAAK,KAAK6C,SAAS,iBACvBnE,OAAA;cAAMyD,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACvEO,IAAI,CAAC3C;YAAK;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACP;UAAA,GAVIE,IAAI,CAAC/C,EAAE;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWR,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAjKIJ,wBAAiE;EAAA,QAQ5CH,WAAW;AAAA;AAAAsE,EAAA,GARhCnE,wBAAiE;AAmKvE,eAAeA,wBAAwB;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}