{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport ProfessionalPageLayout, { ProfessionalContentGrid } from '../components/ProfessionalPageLayout';\nimport sidebarService from '../services/sidebarService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScholarshipsByLevel = () => {\n  _s();\n  const {\n    level\n  } = useParams();\n  const {\n    translations\n  } = useLanguage();\n  const [scholarships, setScholarships] = useState([]);\n  const [statistics, setStatistics] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const decodedLevel = level ? decodeURIComponent(level) : '';\n  useEffect(() => {\n    if (decodedLevel) {\n      fetchScholarshipsByLevel();\n      fetchLevelStatistics();\n    }\n  }, [decodedLevel, currentPage]);\n  const fetchScholarshipsByLevel = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/scholarships?level=${encodeURIComponent(decodedLevel)}&page=${currentPage}&limit=12`);\n      if (response.ok) {\n        var _data$pagination;\n        const data = await response.json();\n        setScholarships(data.data || []);\n        setTotalPages(((_data$pagination = data.pagination) === null || _data$pagination === void 0 ? void 0 : _data$pagination.totalPages) || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships by level:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchLevelStatistics = async () => {\n    try {\n      // Calculate statistics from the scholarships data\n      const totalScholarships = scholarships.length;\n      const openScholarships = scholarships.filter(s => s.isOpen).length;\n      const closedScholarships = totalScholarships - openScholarships;\n      setStatistics({\n        level: decodedLevel,\n        totalScholarships,\n        openScholarships,\n        closedScholarships\n      });\n    } catch (error) {\n      console.error('Error calculating level statistics:', error);\n    }\n  };\n  const handleScholarshipClick = id => {\n    window.location.href = `/scholarships/${id}`;\n  };\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const renderScholarshipItem = scholarship => /*#__PURE__*/_jsxDEV(ScholarshipCard, {\n    id: scholarship.id,\n    title: scholarship.title,\n    thumbnail: scholarship.thumbnail || '',\n    deadline: scholarship.deadline,\n    isOpen: scholarship.isOpen,\n    country: scholarship.country,\n    onClick: handleScholarshipClick\n  }, scholarship.id, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(ProfessionalPageLayout, {\n    hero: {\n      title: `Bourses de ${decodedLevel}`,\n      subtitle: \"Découvrez toutes les opportunités pour ce niveau d'études\",\n      icon: sidebarService.getLevelIcon(decodedLevel),\n      backgroundColor: 'bg-gradient-to-r from-green-600 to-green-800'\n    },\n    statistics: statistics ? {\n      total: statistics.totalScholarships,\n      active: statistics.openScholarships,\n      inactive: statistics.closedScholarships,\n      label: 'Total des bourses',\n      activeLabel: 'Bourses ouvertes',\n      inactiveLabel: 'Bourses fermées'\n    } : undefined,\n    sidebarConfig: {\n      type: 'levels',\n      currentItem: decodedLevel,\n      limit: 10\n    },\n    children: /*#__PURE__*/_jsxDEV(ProfessionalContentGrid, {\n      items: scholarships,\n      loading: loading,\n      emptyMessage: \"Aucune bourse n'est actuellement disponible pour ce niveau d'\\xE9tudes.\",\n      emptyIcon: \"\\uD83C\\uDF93\",\n      renderItem: renderScholarshipItem,\n      pagination: {\n        currentPage,\n        totalPages,\n        onPageChange: handlePageChange\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n_s(ScholarshipsByLevel, \"4cnTpp1JuQzgXIKyhpr7EJZ4Coc=\", false, function () {\n  return [useParams, useLanguage];\n});\n_c = ScholarshipsByLevel;\nexport default ScholarshipsByLevel;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipsByLevel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useLanguage", "ProfessionalPageLayout", "ProfessionalContentGrid", "sidebarService", "jsxDEV", "_jsxDEV", "ScholarshipsByLevel", "_s", "level", "translations", "scholarships", "setScholarships", "statistics", "setStatistics", "loading", "setLoading", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "decodedLevel", "decodeURIComponent", "fetchScholarshipsByLevel", "fetchLevelStatistics", "response", "fetch", "encodeURIComponent", "ok", "_data$pagination", "data", "json", "pagination", "error", "console", "totalScholarships", "length", "openScholarships", "filter", "s", "isOpen", "closedScholarships", "handleScholarshipClick", "id", "window", "location", "href", "handlePageChange", "page", "renderScholarshipItem", "scholarship", "ScholarshipCard", "title", "thumbnail", "deadline", "country", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hero", "subtitle", "icon", "getLevelIcon", "backgroundColor", "total", "active", "inactive", "label", "activeLabel", "inactiveLabel", "undefined", "sidebarConfig", "type", "currentItem", "limit", "children", "items", "emptyMessage", "emptyIcon", "renderItem", "onPageChange", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport ProfessionalPageLayout, { ProfessionalContentGrid } from '../components/ProfessionalPageLayout';\nimport sidebarService from '../services/sidebarService';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  thumbnail?: string;\n  deadline: string;\n  isOpen: boolean;\n  country: string;\n  level: string;\n  description?: string;\n  financialBenefitsSummary?: string;\n  eligibilitySummary?: string;\n}\n\ninterface LevelStatistics {\n  level: string;\n  totalScholarships: number;\n  openScholarships: number;\n  closedScholarships: number;\n}\n\nconst ScholarshipsByLevel: React.FC = () => {\n  const { level } = useParams<{ level: string }>();\n  const { translations } = useLanguage();\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [statistics, setStatistics] = useState<LevelStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  const decodedLevel = level ? decodeURIComponent(level) : '';\n\n  useEffect(() => {\n    if (decodedLevel) {\n      fetchScholarshipsByLevel();\n      fetchLevelStatistics();\n    }\n  }, [decodedLevel, currentPage]);\n\n  const fetchScholarshipsByLevel = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/scholarships?level=${encodeURIComponent(decodedLevel)}&page=${currentPage}&limit=12`);\n      if (response.ok) {\n        const data = await response.json();\n        setScholarships(data.data || []);\n        setTotalPages(data.pagination?.totalPages || 1);\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships by level:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchLevelStatistics = async () => {\n    try {\n      // Calculate statistics from the scholarships data\n      const totalScholarships = scholarships.length;\n      const openScholarships = scholarships.filter(s => s.isOpen).length;\n      const closedScholarships = totalScholarships - openScholarships;\n\n      setStatistics({\n        level: decodedLevel,\n        totalScholarships,\n        openScholarships,\n        closedScholarships\n      });\n    } catch (error) {\n      console.error('Error calculating level statistics:', error);\n    }\n  };\n\n  const handleScholarshipClick = (id: number) => {\n    window.location.href = `/scholarships/${id}`;\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n  };\n\n  const renderScholarshipItem = (scholarship: Scholarship) => (\n    <ScholarshipCard\n      key={scholarship.id}\n      id={scholarship.id}\n      title={scholarship.title}\n      thumbnail={scholarship.thumbnail || ''}\n      deadline={scholarship.deadline}\n      isOpen={scholarship.isOpen}\n      country={scholarship.country}\n      onClick={handleScholarshipClick}\n    />\n  );\n  return (\n    <ProfessionalPageLayout\n      hero={{\n        title: `Bourses de ${decodedLevel}`,\n        subtitle: \"Découvrez toutes les opportunités pour ce niveau d'études\",\n        icon: sidebarService.getLevelIcon(decodedLevel),\n        backgroundColor: 'bg-gradient-to-r from-green-600 to-green-800'\n      }}\n      statistics={statistics ? {\n        total: statistics.totalScholarships,\n        active: statistics.openScholarships,\n        inactive: statistics.closedScholarships,\n        label: 'Total des bourses',\n        activeLabel: 'Bourses ouvertes',\n        inactiveLabel: 'Bourses fermées'\n      } : undefined}\n      sidebarConfig={{\n        type: 'levels',\n        currentItem: decodedLevel,\n        limit: 10\n      }}\n    >\n      <ProfessionalContentGrid\n        items={scholarships}\n        loading={loading}\n        emptyMessage=\"Aucune bourse n'est actuellement disponible pour ce niveau d'études.\"\n        emptyIcon=\"🎓\"\n        renderItem={renderScholarshipItem}\n        pagination={{\n          currentPage,\n          totalPages,\n          onPageChange: handlePageChange\n        }}\n      />\n    </ProfessionalPageLayout>\n  );\n};\n\nexport default ScholarshipsByLevel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,4BAA4B;AAExD,OAAOC,sBAAsB,IAAIC,uBAAuB,QAAQ,sCAAsC;AACtG,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsBxD,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM;IAAEC;EAAM,CAAC,GAAGT,SAAS,CAAoB,CAAC;EAChD,MAAM;IAAEU;EAAa,CAAC,GAAGT,WAAW,CAAC,CAAC;EACtC,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAyB,IAAI,CAAC;EAC1E,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMuB,YAAY,GAAGZ,KAAK,GAAGa,kBAAkB,CAACb,KAAK,CAAC,GAAG,EAAE;EAE3DV,SAAS,CAAC,MAAM;IACd,IAAIsB,YAAY,EAAE;MAChBE,wBAAwB,CAAC,CAAC;MAC1BC,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACH,YAAY,EAAEJ,WAAW,CAAC,CAAC;EAE/B,MAAMM,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,2BAA2BC,kBAAkB,CAACN,YAAY,CAAC,SAASJ,WAAW,WAAW,CAAC;MACxH,IAAIQ,QAAQ,CAACG,EAAE,EAAE;QAAA,IAAAC,gBAAA;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCnB,eAAe,CAACkB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAChCV,aAAa,CAAC,EAAAS,gBAAA,GAAAC,IAAI,CAACE,UAAU,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBV,UAAU,KAAI,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF;MACA,MAAMW,iBAAiB,GAAGxB,YAAY,CAACyB,MAAM;MAC7C,MAAMC,gBAAgB,GAAG1B,YAAY,CAAC2B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,CAAC,CAACJ,MAAM;MAClE,MAAMK,kBAAkB,GAAGN,iBAAiB,GAAGE,gBAAgB;MAE/DvB,aAAa,CAAC;QACZL,KAAK,EAAEY,YAAY;QACnBc,iBAAiB;QACjBE,gBAAgB;QAChBI;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC;EAED,MAAMS,sBAAsB,GAAIC,EAAU,IAAK;IAC7CC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,iBAAiBH,EAAE,EAAE;EAC9C,CAAC;EAED,MAAMI,gBAAgB,GAAIC,IAAY,IAAK;IACzC9B,cAAc,CAAC8B,IAAI,CAAC;EACtB,CAAC;EAED,MAAMC,qBAAqB,GAAIC,WAAwB,iBACrD5C,OAAA,CAAC6C,eAAe;IAEdR,EAAE,EAAEO,WAAW,CAACP,EAAG;IACnBS,KAAK,EAAEF,WAAW,CAACE,KAAM;IACzBC,SAAS,EAAEH,WAAW,CAACG,SAAS,IAAI,EAAG;IACvCC,QAAQ,EAAEJ,WAAW,CAACI,QAAS;IAC/Bd,MAAM,EAAEU,WAAW,CAACV,MAAO;IAC3Be,OAAO,EAAEL,WAAW,CAACK,OAAQ;IAC7BC,OAAO,EAAEd;EAAuB,GAP3BQ,WAAW,CAACP,EAAE;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAQpB,CACF;EACD,oBACEtD,OAAA,CAACJ,sBAAsB;IACrB2D,IAAI,EAAE;MACJT,KAAK,EAAE,cAAc/B,YAAY,EAAE;MACnCyC,QAAQ,EAAE,2DAA2D;MACrEC,IAAI,EAAE3D,cAAc,CAAC4D,YAAY,CAAC3C,YAAY,CAAC;MAC/C4C,eAAe,EAAE;IACnB,CAAE;IACFpD,UAAU,EAAEA,UAAU,GAAG;MACvBqD,KAAK,EAAErD,UAAU,CAACsB,iBAAiB;MACnCgC,MAAM,EAAEtD,UAAU,CAACwB,gBAAgB;MACnC+B,QAAQ,EAAEvD,UAAU,CAAC4B,kBAAkB;MACvC4B,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,kBAAkB;MAC/BC,aAAa,EAAE;IACjB,CAAC,GAAGC,SAAU;IACdC,aAAa,EAAE;MACbC,IAAI,EAAE,QAAQ;MACdC,WAAW,EAAEtD,YAAY;MACzBuD,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,eAEFvE,OAAA,CAACH,uBAAuB;MACtB2E,KAAK,EAAEnE,YAAa;MACpBI,OAAO,EAAEA,OAAQ;MACjBgE,YAAY,EAAC,yEAAsE;MACnFC,SAAS,EAAC,cAAI;MACdC,UAAU,EAAEhC,qBAAsB;MAClCjB,UAAU,EAAE;QACVf,WAAW;QACXE,UAAU;QACV+D,YAAY,EAAEnC;MAChB;IAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACoB,CAAC;AAE7B,CAAC;AAACpD,EAAA,CA5GID,mBAA6B;EAAA,QACfP,SAAS,EACFC,WAAW;AAAA;AAAAkF,EAAA,GAFhC5E,mBAA6B;AA8GnC,eAAeA,mBAAmB;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}