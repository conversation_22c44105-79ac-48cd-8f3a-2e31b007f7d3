{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Dropdown.tsx\",\n  _s = $RefreshSig$();\n/**\n * Professional Dropdown Component\n * \n * Industry-standard dropdown with proper hover/click interactions,\n * accessibility features, and smooth animations.\n */\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dropdown = ({\n  trigger,\n  items,\n  className = '',\n  dropdownClassName = '',\n  placement = 'bottom-left',\n  showOnHover = true,\n  closeOnClick = true,\n  maxHeight = '300px',\n  loading = false,\n  emptyMessage = 'No items available',\n  onOpen,\n  onClose\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const dropdownRef = useRef(null);\n  const timeoutRef = useRef(null);\n\n  // Check if mobile device\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Handle click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = event => {\n      const target = event.target;\n      if (dropdownRef.current && !dropdownRef.current.contains(target)) {\n        closeDropdown();\n      }\n    };\n    const handleMouseDown = event => handleClickOutside(event);\n    const handleTouchStart = event => handleClickOutside(event);\n    if (isOpen) {\n      document.addEventListener('mousedown', handleMouseDown);\n      document.addEventListener('touchstart', handleTouchStart);\n    }\n    return () => {\n      document.removeEventListener('mousedown', handleMouseDown);\n      document.removeEventListener('touchstart', handleTouchStart);\n    };\n  }, [isOpen]);\n\n  // Handle escape key\n  useEffect(() => {\n    const handleEscape = event => {\n      if (event.key === 'Escape' && isOpen) {\n        closeDropdown();\n      }\n    };\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [isOpen]);\n  const openDropdown = () => {\n    if (!isOpen) {\n      setIsOpen(true);\n      onOpen === null || onOpen === void 0 ? void 0 : onOpen();\n    }\n  };\n  const closeDropdown = () => {\n    if (isOpen) {\n      setIsOpen(false);\n      onClose === null || onClose === void 0 ? void 0 : onClose();\n    }\n  };\n  const handleMouseEnter = () => {\n    if (showOnHover && !isMobile) {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n      // Slight delay for smoother interaction\n      timeoutRef.current = setTimeout(() => {\n        openDropdown();\n      }, 100);\n    }\n  };\n  const handleMouseLeave = () => {\n    if (showOnHover && !isMobile) {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n      timeoutRef.current = setTimeout(() => {\n        closeDropdown();\n      }, 300); // Longer delay to prevent accidental closure\n    }\n  };\n  const handleClick = () => {\n    if (isMobile || !showOnHover) {\n      if (isOpen) {\n        closeDropdown();\n      } else {\n        openDropdown();\n      }\n    }\n  };\n  const handleItemClick = item => {\n    if (item.disabled) return;\n    if (item.onClick) {\n      item.onClick();\n    }\n    if (item.href) {\n      window.location.href = item.href;\n    }\n    if (closeOnClick) {\n      closeDropdown();\n    }\n  };\n  const getPlacementClasses = () => {\n    switch (placement) {\n      case 'bottom-right':\n        return 'right-0';\n      case 'bottom-center':\n        return 'left-1/2 transform -translate-x-1/2';\n      default:\n        return 'left-0';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: dropdownRef,\n    className: `relative inline-block ${className}`,\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: handleClick,\n      className: \"cursor-pointer select-none\",\n      role: \"button\",\n      tabIndex: 0,\n      \"aria-expanded\": isOpen,\n      \"aria-haspopup\": \"true\",\n      onKeyDown: e => {\n        if (e.key === 'Enter' || e.key === ' ') {\n          e.preventDefault();\n          handleClick();\n        }\n      },\n      children: trigger\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n            absolute z-50 mt-2 min-w-[200px] bg-white rounded-lg shadow-xl border border-gray-200\n            transform transition-all duration-300 ease-out\n            animate-in slide-in-from-top-2 fade-in-0\n            ${isOpen ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 -translate-y-2'}\n            ${getPlacementClasses()}\n            ${dropdownClassName}\n          `,\n      style: {\n        maxHeight,\n        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-3 text-center text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-primary mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm mt-2 block\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 13\n      }, this) : items.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-3 text-center text-gray-500 text-sm\",\n        children: emptyMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-1 max-h-80 overflow-y-auto\",\n        children: items.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: () => handleItemClick(item),\n          className: `\n                    px-3 py-2 text-sm cursor-pointer flex items-center justify-between\n                    transition-colors duration-150 ease-in-out\n                    ${item.disabled ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50 hover:text-primary'}\n                  `,\n          role: \"menuitem\",\n          tabIndex: item.disabled ? -1 : 0,\n          onKeyDown: e => {\n            if ((e.key === 'Enter' || e.key === ' ') && !item.disabled) {\n              e.preventDefault();\n              handleItemClick(item);\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 19\n          }, this), item.count !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\",\n            children: item.count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 21\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(Dropdown, \"m2D8qHu4J4Fq2Zv+eDFBl8N+CH8=\");\n_c = Dropdown;\nexport default Dropdown;\nvar _c;\n$RefreshReg$(_c, \"Dropdown\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Dropdown", "trigger", "items", "className", "dropdownClassName", "placement", "showOnHover", "closeOnClick", "maxHeight", "loading", "emptyMessage", "onOpen", "onClose", "_s", "isOpen", "setIsOpen", "isMobile", "setIsMobile", "dropdownRef", "timeoutRef", "checkMobile", "window", "innerWidth", "addEventListener", "removeEventListener", "handleClickOutside", "event", "target", "current", "contains", "closeDropdown", "handleMouseDown", "handleTouchStart", "document", "handleEscape", "key", "openDropdown", "handleMouseEnter", "clearTimeout", "setTimeout", "handleMouseLeave", "handleClick", "handleItemClick", "item", "disabled", "onClick", "href", "location", "getPlacementClasses", "ref", "onMouseEnter", "onMouseLeave", "children", "role", "tabIndex", "onKeyDown", "e", "preventDefault", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "boxShadow", "length", "map", "label", "count", "undefined", "id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Dropdown.tsx"], "sourcesContent": ["/**\n * Professional Dropdown Component\n * \n * Industry-standard dropdown with proper hover/click interactions,\n * accessibility features, and smooth animations.\n */\n\nimport React, { useState, useRef, useEffect, ReactNode } from 'react';\n\nexport interface DropdownItem {\n  id: string;\n  label: string;\n  href?: string;\n  onClick?: () => void;\n  count?: number;\n  icon?: ReactNode;\n  disabled?: boolean;\n}\n\nexport interface DropdownProps {\n  trigger: ReactNode;\n  items: DropdownItem[];\n  className?: string;\n  dropdownClassName?: string;\n  placement?: 'bottom-left' | 'bottom-right' | 'bottom-center';\n  showOnHover?: boolean;\n  closeOnClick?: boolean;\n  maxHeight?: string;\n  loading?: boolean;\n  emptyMessage?: string;\n  onOpen?: () => void;\n  onClose?: () => void;\n}\n\nconst Dropdown: React.FC<DropdownProps> = ({\n  trigger,\n  items,\n  className = '',\n  dropdownClassName = '',\n  placement = 'bottom-left',\n  showOnHover = true,\n  closeOnClick = true,\n  maxHeight = '300px',\n  loading = false,\n  emptyMessage = 'No items available',\n  onOpen,\n  onClose\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const timeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Check if mobile device\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n    \n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Handle click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent | TouchEvent) => {\n      const target = event.target as Node;\n      if (dropdownRef.current && !dropdownRef.current.contains(target)) {\n        closeDropdown();\n      }\n    };\n\n    const handleMouseDown = (event: MouseEvent) => handleClickOutside(event);\n    const handleTouchStart = (event: TouchEvent) => handleClickOutside(event);\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleMouseDown);\n      document.addEventListener('touchstart', handleTouchStart);\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleMouseDown);\n      document.removeEventListener('touchstart', handleTouchStart);\n    };\n  }, [isOpen]);\n\n  // Handle escape key\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isOpen) {\n        closeDropdown();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [isOpen]);\n\n  const openDropdown = () => {\n    if (!isOpen) {\n      setIsOpen(true);\n      onOpen?.();\n    }\n  };\n\n  const closeDropdown = () => {\n    if (isOpen) {\n      setIsOpen(false);\n      onClose?.();\n    }\n  };\n\n  const handleMouseEnter = () => {\n    if (showOnHover && !isMobile) {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n      // Slight delay for smoother interaction\n      timeoutRef.current = setTimeout(() => {\n        openDropdown();\n      }, 100);\n    }\n  };\n\n  const handleMouseLeave = () => {\n    if (showOnHover && !isMobile) {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n      timeoutRef.current = setTimeout(() => {\n        closeDropdown();\n      }, 300); // Longer delay to prevent accidental closure\n    }\n  };\n\n  const handleClick = () => {\n    if (isMobile || !showOnHover) {\n      if (isOpen) {\n        closeDropdown();\n      } else {\n        openDropdown();\n      }\n    }\n  };\n\n  const handleItemClick = (item: DropdownItem) => {\n    if (item.disabled) return;\n    \n    if (item.onClick) {\n      item.onClick();\n    }\n    \n    if (item.href) {\n      window.location.href = item.href;\n    }\n    \n    if (closeOnClick) {\n      closeDropdown();\n    }\n  };\n\n  const getPlacementClasses = () => {\n    switch (placement) {\n      case 'bottom-right':\n        return 'right-0';\n      case 'bottom-center':\n        return 'left-1/2 transform -translate-x-1/2';\n      default:\n        return 'left-0';\n    }\n  };\n\n  return (\n    <div\n      ref={dropdownRef}\n      className={`relative inline-block ${className}`}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n    >\n      {/* Trigger */}\n      <div\n        onClick={handleClick}\n        className=\"cursor-pointer select-none\"\n        role=\"button\"\n        tabIndex={0}\n        aria-expanded={isOpen}\n        aria-haspopup=\"true\"\n        onKeyDown={(e) => {\n          if (e.key === 'Enter' || e.key === ' ') {\n            e.preventDefault();\n            handleClick();\n          }\n        }}\n      >\n        {trigger}\n      </div>\n\n      {/* Dropdown Menu */}\n      {isOpen && (\n        <div\n          className={`\n            absolute z-50 mt-2 min-w-[200px] bg-white rounded-lg shadow-xl border border-gray-200\n            transform transition-all duration-300 ease-out\n            animate-in slide-in-from-top-2 fade-in-0\n            ${isOpen ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 -translate-y-2'}\n            ${getPlacementClasses()}\n            ${dropdownClassName}\n          `}\n          style={{\n            maxHeight,\n            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n          }}\n        >\n          {loading ? (\n            <div className=\"px-4 py-3 text-center text-gray-500\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-primary mx-auto\"></div>\n              <span className=\"text-sm mt-2 block\">Loading...</span>\n            </div>\n          ) : items.length === 0 ? (\n            <div className=\"px-4 py-3 text-center text-gray-500 text-sm\">\n              {emptyMessage}\n            </div>\n          ) : (\n            <div className=\"py-1 max-h-80 overflow-y-auto\">\n              {items.map((item) => (\n                <div\n                  key={item.id}\n                  onClick={() => handleItemClick(item)}\n                  className={`\n                    px-3 py-2 text-sm cursor-pointer flex items-center justify-between\n                    transition-colors duration-150 ease-in-out\n                    ${item.disabled\n                      ? 'text-gray-400 cursor-not-allowed'\n                      : 'text-gray-700 hover:bg-gray-50 hover:text-primary'\n                    }\n                  `}\n                  role=\"menuitem\"\n                  tabIndex={item.disabled ? -1 : 0}\n                  onKeyDown={(e) => {\n                    if ((e.key === 'Enter' || e.key === ' ') && !item.disabled) {\n                      e.preventDefault();\n                      handleItemClick(item);\n                    }\n                  }}\n                >\n                  <span>{item.label}</span>\n                  {item.count !== undefined && (\n                    <span className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\">\n                      {item.count}\n                    </span>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Dropdown;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAmB,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA2BtE,MAAMC,QAAiC,GAAGA,CAAC;EACzCC,OAAO;EACPC,KAAK;EACLC,SAAS,GAAG,EAAE;EACdC,iBAAiB,GAAG,EAAE;EACtBC,SAAS,GAAG,aAAa;EACzBC,WAAW,GAAG,IAAI;EAClBC,YAAY,GAAG,IAAI;EACnBC,SAAS,GAAG,OAAO;EACnBC,OAAO,GAAG,KAAK;EACfC,YAAY,GAAG,oBAAoB;EACnCC,MAAM;EACNC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMuB,WAAW,GAAGtB,MAAM,CAAiB,IAAI,CAAC;EAChD,MAAMuB,UAAU,GAAGvB,MAAM,CAAwB,IAAI,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMuB,WAAW,GAAGA,CAAA,KAAM;MACxBH,WAAW,CAACI,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;IACtC,CAAC;IAEDF,WAAW,CAAC,CAAC;IACbC,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,WAAW,CAAC;IAC9C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,WAAW,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvB,SAAS,CAAC,MAAM;IACd,MAAM4B,kBAAkB,GAAIC,KAA8B,IAAK;MAC7D,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAc;MACnC,IAAIT,WAAW,CAACU,OAAO,IAAI,CAACV,WAAW,CAACU,OAAO,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;QAChEG,aAAa,CAAC,CAAC;MACjB;IACF,CAAC;IAED,MAAMC,eAAe,GAAIL,KAAiB,IAAKD,kBAAkB,CAACC,KAAK,CAAC;IACxE,MAAMM,gBAAgB,GAAIN,KAAiB,IAAKD,kBAAkB,CAACC,KAAK,CAAC;IAEzE,IAAIZ,MAAM,EAAE;MACVmB,QAAQ,CAACV,gBAAgB,CAAC,WAAW,EAAEQ,eAAe,CAAC;MACvDE,QAAQ,CAACV,gBAAgB,CAAC,YAAY,EAAES,gBAAgB,CAAC;IAC3D;IAEA,OAAO,MAAM;MACXC,QAAQ,CAACT,mBAAmB,CAAC,WAAW,EAAEO,eAAe,CAAC;MAC1DE,QAAQ,CAACT,mBAAmB,CAAC,YAAY,EAAEQ,gBAAgB,CAAC;IAC9D,CAAC;EACH,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;;EAEZ;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMqC,YAAY,GAAIR,KAAoB,IAAK;MAC7C,IAAIA,KAAK,CAACS,GAAG,KAAK,QAAQ,IAAIrB,MAAM,EAAE;QACpCgB,aAAa,CAAC,CAAC;MACjB;IACF,CAAC;IAEDG,QAAQ,CAACV,gBAAgB,CAAC,SAAS,EAAEW,YAAY,CAAC;IAClD,OAAO,MAAMD,QAAQ,CAACT,mBAAmB,CAAC,SAAS,EAAEU,YAAY,CAAC;EACpE,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EAEZ,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtB,MAAM,EAAE;MACXC,SAAS,CAAC,IAAI,CAAC;MACfJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,CAAC;IACZ;EACF,CAAC;EAED,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIhB,MAAM,EAAE;MACVC,SAAS,CAAC,KAAK,CAAC;MAChBH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,CAAC;IACb;EACF,CAAC;EAED,MAAMyB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/B,WAAW,IAAI,CAACU,QAAQ,EAAE;MAC5B,IAAIG,UAAU,CAACS,OAAO,EAAE;QACtBU,YAAY,CAACnB,UAAU,CAACS,OAAO,CAAC;MAClC;MACA;MACAT,UAAU,CAACS,OAAO,GAAGW,UAAU,CAAC,MAAM;QACpCH,YAAY,CAAC,CAAC;MAChB,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIlC,WAAW,IAAI,CAACU,QAAQ,EAAE;MAC5B,IAAIG,UAAU,CAACS,OAAO,EAAE;QACtBU,YAAY,CAACnB,UAAU,CAACS,OAAO,CAAC;MAClC;MACAT,UAAU,CAACS,OAAO,GAAGW,UAAU,CAAC,MAAM;QACpCT,aAAa,CAAC,CAAC;MACjB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIzB,QAAQ,IAAI,CAACV,WAAW,EAAE;MAC5B,IAAIQ,MAAM,EAAE;QACVgB,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLM,YAAY,CAAC,CAAC;MAChB;IACF;EACF,CAAC;EAED,MAAMM,eAAe,GAAIC,IAAkB,IAAK;IAC9C,IAAIA,IAAI,CAACC,QAAQ,EAAE;IAEnB,IAAID,IAAI,CAACE,OAAO,EAAE;MAChBF,IAAI,CAACE,OAAO,CAAC,CAAC;IAChB;IAEA,IAAIF,IAAI,CAACG,IAAI,EAAE;MACbzB,MAAM,CAAC0B,QAAQ,CAACD,IAAI,GAAGH,IAAI,CAACG,IAAI;IAClC;IAEA,IAAIvC,YAAY,EAAE;MAChBuB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMkB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,QAAQ3C,SAAS;MACf,KAAK,cAAc;QACjB,OAAO,SAAS;MAClB,KAAK,eAAe;QAClB,OAAO,qCAAqC;MAC9C;QACE,OAAO,QAAQ;IACnB;EACF,CAAC;EAED,oBACEN,OAAA;IACEkD,GAAG,EAAE/B,WAAY;IACjBf,SAAS,EAAE,yBAAyBA,SAAS,EAAG;IAChD+C,YAAY,EAAEb,gBAAiB;IAC/Bc,YAAY,EAAEX,gBAAiB;IAAAY,QAAA,gBAG/BrD,OAAA;MACE8C,OAAO,EAAEJ,WAAY;MACrBtC,SAAS,EAAC,4BAA4B;MACtCkD,IAAI,EAAC,QAAQ;MACbC,QAAQ,EAAE,CAAE;MACZ,iBAAexC,MAAO;MACtB,iBAAc,MAAM;MACpByC,SAAS,EAAGC,CAAC,IAAK;QAChB,IAAIA,CAAC,CAACrB,GAAG,KAAK,OAAO,IAAIqB,CAAC,CAACrB,GAAG,KAAK,GAAG,EAAE;UACtCqB,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBhB,WAAW,CAAC,CAAC;QACf;MACF,CAAE;MAAAW,QAAA,EAEDnD;IAAO;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL/C,MAAM,iBACLf,OAAA;MACEI,SAAS,EAAE;AACrB;AACA;AACA;AACA,cAAcW,MAAM,GAAG,qCAAqC,GAAG,mCAAmC;AAClG,cAAckC,mBAAmB,CAAC,CAAC;AACnC,cAAc5C,iBAAiB;AAC/B,WAAY;MACF0D,KAAK,EAAE;QACLtD,SAAS;QACTuD,SAAS,EAAE;MACb,CAAE;MAAAX,QAAA,EAED3C,OAAO,gBACNV,OAAA;QAAKI,SAAS,EAAC,qCAAqC;QAAAiD,QAAA,gBAClDrD,OAAA;UAAKI,SAAS,EAAC;QAAqE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3F9D,OAAA;UAAMI,SAAS,EAAC,oBAAoB;UAAAiD,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,GACJ3D,KAAK,CAAC8D,MAAM,KAAK,CAAC,gBACpBjE,OAAA;QAAKI,SAAS,EAAC,6CAA6C;QAAAiD,QAAA,EACzD1C;MAAY;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAEN9D,OAAA;QAAKI,SAAS,EAAC,+BAA+B;QAAAiD,QAAA,EAC3ClD,KAAK,CAAC+D,GAAG,CAAEtB,IAAI,iBACd5C,OAAA;UAEE8C,OAAO,EAAEA,CAAA,KAAMH,eAAe,CAACC,IAAI,CAAE;UACrCxC,SAAS,EAAE;AAC7B;AACA;AACA,sBAAsBwC,IAAI,CAACC,QAAQ,GACX,kCAAkC,GAClC,mDAAmD;AAC3E,mBACoB;UACFS,IAAI,EAAC,UAAU;UACfC,QAAQ,EAAEX,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAE;UACjCW,SAAS,EAAGC,CAAC,IAAK;YAChB,IAAI,CAACA,CAAC,CAACrB,GAAG,KAAK,OAAO,IAAIqB,CAAC,CAACrB,GAAG,KAAK,GAAG,KAAK,CAACQ,IAAI,CAACC,QAAQ,EAAE;cAC1DY,CAAC,CAACC,cAAc,CAAC,CAAC;cAClBf,eAAe,CAACC,IAAI,CAAC;YACvB;UACF,CAAE;UAAAS,QAAA,gBAEFrD,OAAA;YAAAqD,QAAA,EAAOT,IAAI,CAACuB;UAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxBlB,IAAI,CAACwB,KAAK,KAAKC,SAAS,iBACvBrE,OAAA;YAAMI,SAAS,EAAC,0DAA0D;YAAAiD,QAAA,EACvET,IAAI,CAACwB;UAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACP;QAAA,GAxBIlB,IAAI,CAAC0B,EAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChD,EAAA,CAlOIb,QAAiC;AAAAsE,EAAA,GAAjCtE,QAAiC;AAoOvC,eAAeA,QAAQ;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}