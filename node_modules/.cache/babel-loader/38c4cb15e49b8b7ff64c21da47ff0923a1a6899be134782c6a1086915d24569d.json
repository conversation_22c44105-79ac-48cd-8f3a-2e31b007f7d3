{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"14\",\n  y: \"14\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"1b0bso\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"2\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"1x09vl\"\n}], [\"path\", {\n  d: \"M7 14v1a2 2 0 0 0 2 2h1\",\n  key: \"pao6x6\"\n}], [\"path\", {\n  d: \"M14 7h1a2 2 0 0 1 2 2v1\",\n  key: \"19tdru\"\n}]];\nconst SendToBack = createLucideIcon(\"send-to-back\", __iconNode);\nexport { __iconNode, SendToBack as default };", "map": {"version": 3, "names": ["__iconNode", "x", "y", "width", "height", "rx", "key", "d", "SendToBack", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/send-to-back.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { x: '14', y: '14', width: '8', height: '8', rx: '2', key: '1b0bso' }],\n  ['rect', { x: '2', y: '2', width: '8', height: '8', rx: '2', key: '1x09vl' }],\n  ['path', { d: 'M7 14v1a2 2 0 0 0 2 2h1', key: 'pao6x6' }],\n  ['path', { d: 'M14 7h1a2 2 0 0 1 2 2v1', key: '19tdru' }],\n];\n\n/**\n * @component @name SendToBack\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIxNCIgeT0iMTQiIHdpZHRoPSI4IiBoZWlnaHQ9IjgiIHJ4PSIyIiAvPgogIDxyZWN0IHg9IjIiIHk9IjIiIHdpZHRoPSI4IiBoZWlnaHQ9IjgiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik03IDE0djFhMiAyIDAgMCAwIDIgMmgxIiAvPgogIDxwYXRoIGQ9Ik0xNCA3aDFhMiAyIDAgMCAxIDIgMnYxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send-to-back\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SendToBack = createLucideIcon('send-to-back', __iconNode);\n\nexport default SendToBack;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAG;EAAMC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAQ;EAAEL,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAU,GAC1D;AAaM,MAAAE,UAAA,GAAaC,gBAAiB,iBAAgBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}