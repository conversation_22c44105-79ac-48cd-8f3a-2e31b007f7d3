{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 6H3\",\n  key: \"1jwq7v\"\n}], [\"path\", {\n  d: \"M10 12H3\",\n  key: \"1ulcyk\"\n}], [\"path\", {\n  d: \"M10 18H3\",\n  key: \"13769t\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"15\",\n  r: \"3\",\n  key: \"1upz2a\"\n}], [\"path\", {\n  d: \"m21 19-1.9-1.9\",\n  key: \"dwi7p8\"\n}]];\nconst TextSearch = createLucideIcon(\"text-search\", __iconNode);\nexport { __iconNode, TextSearch as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "TextSearch", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/text-search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 6H3', key: '1jwq7v' }],\n  ['path', { d: 'M10 12H3', key: '1ulcyk' }],\n  ['path', { d: 'M10 18H3', key: '13769t' }],\n  ['circle', { cx: '17', cy: '15', r: '3', key: '1upz2a' }],\n  ['path', { d: 'm21 19-1.9-1.9', key: 'dwi7p8' }],\n];\n\n/**\n * @component @name TextSearch\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgNkgzIiAvPgogIDxwYXRoIGQ9Ik0xMCAxMkgzIiAvPgogIDxwYXRoIGQ9Ik0xMCAxOEgzIiAvPgogIDxjaXJjbGUgY3g9IjE3IiBjeT0iMTUiIHI9IjMiIC8+CiAgPHBhdGggZD0ibTIxIDE5LTEuOS0xLjkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/text-search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TextSearch = createLucideIcon('text-search', __iconNode);\n\nexport default TextSearch;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAU,GACjD;AAaM,MAAAI,UAAA,GAAaC,gBAAiB,gBAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}