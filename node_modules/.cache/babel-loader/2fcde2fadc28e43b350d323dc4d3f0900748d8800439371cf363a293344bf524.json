{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 10-6.157 6.162a2 2 0 0 0-.5.833l-1.322 4.36a.5.5 0 0 0 .622.624l4.358-1.323a2 2 0 0 0 .83-.5L14 13.982\",\n  key: \"bjo8r8\"\n}], [\"path\", {\n  d: \"m12.829 7.172 4.359-4.346a1 1 0 1 1 3.986 3.986l-4.353 4.353\",\n  key: \"16h5ne\"\n}], [\"path\", {\n  d: \"m15 5 4 4\",\n  key: \"1mk7zo\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst PencilOff = createLucideIcon(\"pencil-off\", __iconNode);\nexport { __iconNode, PencilOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/pencil-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm10 10-6.157 6.162a2 2 0 0 0-.5.833l-1.322 4.36a.5.5 0 0 0 .622.624l4.358-1.323a2 2 0 0 0 .83-.5L14 13.982',\n      key: 'bjo8r8',\n    },\n  ],\n  ['path', { d: 'm12.829 7.172 4.359-4.346a1 1 0 1 1 3.986 3.986l-4.353 4.353', key: '16h5ne' }],\n  ['path', { d: 'm15 5 4 4', key: '1mk7zo' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n];\n\n/**\n * @component @name PencilOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAgMTAtNi4xNTcgNi4xNjJhMiAyIDAgMCAwLS41LjgzM2wtMS4zMjIgNC4zNmEuNS41IDAgMCAwIC42MjIuNjI0bDQuMzU4LTEuMzIzYTIgMiAwIDAgMCAuODMtLjVMMTQgMTMuOTgyIiAvPgogIDxwYXRoIGQ9Im0xMi44MjkgNy4xNzIgNC4zNTktNC4zNDZhMSAxIDAgMSAxIDMuOTg2IDMuOTg2bC00LjM1MyA0LjM1MyIgLz4KICA8cGF0aCBkPSJtMTUgNSA0IDQiIC8+CiAgPHBhdGggZD0ibTIgMiAyMCAyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pencil-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PencilOff = createLucideIcon('pencil-off', __iconNode);\n\nexport default PencilOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,8DAAgE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7F,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,eAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}