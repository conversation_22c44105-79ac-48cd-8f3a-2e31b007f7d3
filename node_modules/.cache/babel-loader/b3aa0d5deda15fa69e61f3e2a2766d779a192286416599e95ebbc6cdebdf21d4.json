{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7.9 20A9 9 0 1 0 4 16.1L2 22Z\",\n  key: \"vv11sd\"\n}], [\"path\", {\n  d: \"m10 15-3-3 3-3\",\n  key: \"1pgupc\"\n}], [\"path\", {\n  d: \"M7 12h7a2 2 0 0 1 2 2v1\",\n  key: \"1gheu4\"\n}]];\nconst MessageCircleReply = createLucideIcon(\"message-circle-reply\", __iconNode);\nexport { __iconNode, MessageCircleReply as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageCircleReply", "createLucideIcon"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/node_modules/lucide-react/src/icons/message-circle-reply.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M7.9 20A9 9 0 1 0 4 16.1L2 22Z', key: 'vv11sd' }],\n  ['path', { d: 'm10 15-3-3 3-3', key: '1pgupc' }],\n  ['path', { d: 'M7 12h7a2 2 0 0 1 2 2v1', key: '1gheu4' }],\n];\n\n/**\n * @component @name MessageCircleReply\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNy45IDIwQTkgOSAwIDEgMCA0IDE2LjFMMiAyMloiIC8+CiAgPHBhdGggZD0ibTEwIDE1LTMtMyAzLTMiIC8+CiAgPHBhdGggZD0iTTcgMTJoN2EyIDIgMCAwIDEgMiAydjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle-reply\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircleReply = createLucideIcon('message-circle-reply', __iconNode);\n\nexport default MessageCircleReply;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAU,GAC1D;AAaM,MAAAC,kBAAA,GAAqBC,gBAAiB,yBAAwBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}