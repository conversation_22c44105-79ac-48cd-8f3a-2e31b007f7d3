{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx\",\n  _s = $RefreshSig$();\n/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport Dropdown from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavigationDropdown = ({\n  type,\n  label,\n  className = ''\n}) => {\n  _s();\n  const [items, setItems] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const {\n    translations\n  } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n\n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor = () => [];\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = countries => [{\n            id: 'all-countries',\n            label: translations.navigation.allCountries || 'All Countries',\n            href: '/countries',\n            count: countries.reduce((sum, c) => sum + c.count, 0)\n          }, ...countries.slice(0, 8).map(country => ({\n            id: country.slug,\n            label: country.name,\n            href: `/countries/${encodeURIComponent(country.name)}`,\n            count: country.count\n          })), ...(countries.length > 8 ? [{\n            id: 'view-all-countries',\n            label: translations.navigation.viewAll || 'View All',\n            href: '/countries'\n          }] : [])];\n          break;\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = levels => [{\n            id: 'all-scholarships',\n            label: translations.navigation.allScholarships || 'All Scholarships',\n            href: '/scholarships',\n            count: levels.reduce((sum, l) => sum + l.count, 0)\n          }, ...levels.map(level => ({\n            id: level.slug,\n            label: level.name,\n            href: `/scholarships?level=${encodeURIComponent(level.name)}`,\n            count: level.openCount\n          }))];\n          break;\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = types => [{\n            id: 'all-opportunities',\n            label: translations.navigation.allOpportunities || 'All Opportunities',\n            href: '/opportunities',\n            count: types.reduce((sum, t) => sum + t.count, 0)\n          }, ...types.map(opType => ({\n            id: opType.slug,\n            label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n            href: `/opportunities?type=${encodeURIComponent(opType.name)}`,\n            count: opType.activeCount\n          }))];\n          break;\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      const result = await response.json();\n      const data = result.data || result;\n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get the main page URL for this navigation type\n  const getMainPageUrl = () => {\n    switch (type) {\n      case 'countries':\n        return '/countries';\n      case 'scholarships':\n        return '/scholarships';\n      case 'opportunities':\n        return '/opportunities';\n      default:\n        return '/';\n    }\n  };\n  const trigger = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      flex items-center rounded-md text-sm font-medium\n      transition-all duration-200 ease-in-out\n      ${className}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: getMainPageUrl(),\n      className: \"px-3 py-2 text-gray-700 hover:text-primary hover:bg-primary/5 transition-colors duration-200\",\n      onClick: e => e.stopPropagation() // Prevent dropdown from opening when clicking the link\n      ,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"transition-colors duration-200\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-1 py-2 text-gray-400 hover:text-primary transition-colors duration-200 cursor-pointer\",\n      onClick: e => {\n        e.stopPropagation();\n        // This will be handled by the dropdown's click handler\n      },\n      children: /*#__PURE__*/_jsxDEV(ChevronDown, {\n        size: 14,\n        className: \"transition-all duration-200 ease-out group-hover:rotate-180\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dropdown, {\n    trigger: trigger,\n    items: items,\n    loading: loading,\n    onOpen: fetchData,\n    showOnHover: true,\n    closeOnClick: true,\n    placement: \"bottom-left\",\n    className: \"group\",\n    dropdownClassName: \"border-t-2 border-primary\",\n    emptyMessage: `No ${type} available`\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n_s(NavigationDropdown, \"dSYAOEQJpmUEaAadZBeAZJtT6G0=\", false, function () {\n  return [useLanguage];\n});\n_c = NavigationDropdown;\nexport default NavigationDropdown;\nvar _c;\n$RefreshReg$(_c, \"NavigationDropdown\");", "map": {"version": 3, "names": ["React", "useState", "Link", "ChevronDown", "Dropdown", "useLanguage", "jsxDEV", "_jsxDEV", "NavigationDropdown", "type", "label", "className", "_s", "items", "setItems", "loading", "setLoading", "translations", "fetchData", "length", "endpoint", "dataProcessor", "countries", "id", "navigation", "allCountries", "href", "count", "reduce", "sum", "c", "slice", "map", "country", "slug", "name", "encodeURIComponent", "viewAll", "levels", "allScholarships", "l", "level", "openCount", "types", "allOpportunities", "t", "opType", "char<PERSON>t", "toUpperCase", "activeCount", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "result", "json", "data", "error", "console", "disabled", "getMainPageUrl", "trigger", "children", "to", "onClick", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onOpen", "showOnHover", "closeOnClick", "placement", "dropdownClassName", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx"], "sourcesContent": ["/**\n * Navigation Dropdown Component\n * \n * Specialized dropdown for navigation menus with data fetching,\n * proper routing, and professional styling.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ChevronDown } from 'lucide-react';\nimport Dropdown, { DropdownItem } from '../common/Dropdown';\nimport { useLanguage } from '../../context/LanguageContext';\n\ninterface NavigationDropdownProps {\n  type: 'countries' | 'scholarships' | 'opportunities';\n  label: string;\n  className?: string;\n}\n\ninterface CountryData {\n  name: string;\n  count: number;\n  slug: string;\n}\n\ninterface LevelData {\n  name: string;\n  count: number;\n  openCount: number;\n  slug: string;\n}\n\ninterface OpportunityTypeData {\n  name: string;\n  count: number;\n  activeCount: number;\n  slug: string;\n}\n\nconst NavigationDropdown: React.FC<NavigationDropdownProps> = ({\n  type,\n  label,\n  className = ''\n}) => {\n  const [items, setItems] = useState<DropdownItem[]>([]);\n  const [loading, setLoading] = useState(false);\n  const { translations } = useLanguage();\n\n  // Fetch data when dropdown opens\n  const fetchData = async () => {\n    if (items.length > 0) return; // Don't refetch if we already have data\n    \n    setLoading(true);\n    try {\n      let endpoint = '';\n      let dataProcessor: (data: any[]) => DropdownItem[] = () => [];\n\n      switch (type) {\n        case 'countries':\n          endpoint = '/api/countries';\n          dataProcessor = (countries: CountryData[]) => [\n            {\n              id: 'all-countries',\n              label: translations.navigation.allCountries || 'All Countries',\n              href: '/countries',\n              count: countries.reduce((sum, c) => sum + c.count, 0)\n            },\n            ...countries.slice(0, 8).map(country => ({\n              id: country.slug,\n              label: country.name,\n              href: `/countries/${encodeURIComponent(country.name)}`,\n              count: country.count\n            })),\n            ...(countries.length > 8 ? [{\n              id: 'view-all-countries',\n              label: translations.navigation.viewAll || 'View All',\n              href: '/countries'\n            }] : [])\n          ];\n          break;\n\n        case 'scholarships':\n          endpoint = '/api/scholarships/levels';\n          dataProcessor = (levels: LevelData[]) => [\n            {\n              id: 'all-scholarships',\n              label: translations.navigation.allScholarships || 'All Scholarships',\n              href: '/scholarships',\n              count: levels.reduce((sum, l) => sum + l.count, 0)\n            },\n            ...levels.map(level => ({\n              id: level.slug,\n              label: level.name,\n              href: `/scholarships?level=${encodeURIComponent(level.name)}`,\n              count: level.openCount\n            }))\n          ];\n          break;\n\n        case 'opportunities':\n          endpoint = '/api/opportunities/types';\n          dataProcessor = (types: OpportunityTypeData[]) => [\n            {\n              id: 'all-opportunities',\n              label: translations.navigation.allOpportunities || 'All Opportunities',\n              href: '/opportunities',\n              count: types.reduce((sum, t) => sum + t.count, 0)\n            },\n            ...types.map(opType => ({\n              id: opType.slug,\n              label: opType.name.charAt(0).toUpperCase() + opType.name.slice(1),\n              href: `/opportunities?type=${encodeURIComponent(opType.name)}`,\n              count: opType.activeCount\n            }))\n          ];\n          break;\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${endpoint}`);\n      if (!response.ok) throw new Error('Failed to fetch data');\n      \n      const result = await response.json();\n      const data = result.data || result;\n      \n      setItems(dataProcessor(data));\n    } catch (error) {\n      console.error(`Error fetching ${type} data:`, error);\n      setItems([{\n        id: 'error',\n        label: 'Failed to load data',\n        disabled: true\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get the main page URL for this navigation type\n  const getMainPageUrl = () => {\n    switch (type) {\n      case 'countries':\n        return '/countries';\n      case 'scholarships':\n        return '/scholarships';\n      case 'opportunities':\n        return '/opportunities';\n      default:\n        return '/';\n    }\n  };\n\n  const trigger = (\n    <div className={`\n      flex items-center rounded-md text-sm font-medium\n      transition-all duration-200 ease-in-out\n      ${className}\n    `}>\n      {/* Main clickable link */}\n      <Link\n        to={getMainPageUrl()}\n        className=\"px-3 py-2 text-gray-700 hover:text-primary hover:bg-primary/5 transition-colors duration-200\"\n        onClick={(e) => e.stopPropagation()} // Prevent dropdown from opening when clicking the link\n      >\n        <span className=\"transition-colors duration-200\">{label}</span>\n      </Link>\n\n      {/* Dropdown arrow - separate clickable area */}\n      <div\n        className=\"px-1 py-2 text-gray-400 hover:text-primary transition-colors duration-200 cursor-pointer\"\n        onClick={(e) => {\n          e.stopPropagation();\n          // This will be handled by the dropdown's click handler\n        }}\n      >\n        <ChevronDown\n          size={14}\n          className=\"transition-all duration-200 ease-out group-hover:rotate-180\"\n        />\n      </div>\n    </div>\n  );\n\n  return (\n    <Dropdown\n      trigger={trigger}\n      items={items}\n      loading={loading}\n      onOpen={fetchData}\n      showOnHover={true}\n      closeOnClick={true}\n      placement=\"bottom-left\"\n      className=\"group\"\n      dropdownClassName=\"border-t-2 border-primary\"\n      emptyMessage={`No ${type} available`}\n    />\n  );\n};\n\nexport default NavigationDropdown;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,QAAQ,MAAwB,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA4B5D,MAAMC,kBAAqD,GAAGA,CAAC;EAC7DC,IAAI;EACJC,KAAK;EACLC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAiB,EAAE,CAAC;EACtD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEgB;EAAa,CAAC,GAAGZ,WAAW,CAAC,CAAC;;EAEtC;EACA,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;;IAE9BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAII,QAAQ,GAAG,EAAE;MACjB,IAAIC,aAA8C,GAAGA,CAAA,KAAM,EAAE;MAE7D,QAAQZ,IAAI;QACV,KAAK,WAAW;UACdW,QAAQ,GAAG,gBAAgB;UAC3BC,aAAa,GAAIC,SAAwB,IAAK,CAC5C;YACEC,EAAE,EAAE,eAAe;YACnBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACC,YAAY,IAAI,eAAe;YAC9DC,IAAI,EAAE,YAAY;YAClBC,KAAK,EAAEL,SAAS,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACH,KAAK,EAAE,CAAC;UACtD,CAAC,EACD,GAAGL,SAAS,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,OAAO,KAAK;YACvCV,EAAE,EAAEU,OAAO,CAACC,IAAI;YAChBxB,KAAK,EAAEuB,OAAO,CAACE,IAAI;YACnBT,IAAI,EAAE,cAAcU,kBAAkB,CAACH,OAAO,CAACE,IAAI,CAAC,EAAE;YACtDR,KAAK,EAAEM,OAAO,CAACN;UACjB,CAAC,CAAC,CAAC,EACH,IAAIL,SAAS,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;YAC1BI,EAAE,EAAE,oBAAoB;YACxBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACa,OAAO,IAAI,UAAU;YACpDX,IAAI,EAAE;UACR,CAAC,CAAC,GAAG,EAAE,CAAC,CACT;UACD;QAEF,KAAK,cAAc;UACjBN,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIiB,MAAmB,IAAK,CACvC;YACEf,EAAE,EAAE,kBAAkB;YACtBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACe,eAAe,IAAI,kBAAkB;YACpEb,IAAI,EAAE,eAAe;YACrBC,KAAK,EAAEW,MAAM,CAACV,MAAM,CAAC,CAACC,GAAG,EAAEW,CAAC,KAAKX,GAAG,GAAGW,CAAC,CAACb,KAAK,EAAE,CAAC;UACnD,CAAC,EACD,GAAGW,MAAM,CAACN,GAAG,CAACS,KAAK,KAAK;YACtBlB,EAAE,EAAEkB,KAAK,CAACP,IAAI;YACdxB,KAAK,EAAE+B,KAAK,CAACN,IAAI;YACjBT,IAAI,EAAE,uBAAuBU,kBAAkB,CAACK,KAAK,CAACN,IAAI,CAAC,EAAE;YAC7DR,KAAK,EAAEc,KAAK,CAACC;UACf,CAAC,CAAC,CAAC,CACJ;UACD;QAEF,KAAK,eAAe;UAClBtB,QAAQ,GAAG,0BAA0B;UACrCC,aAAa,GAAIsB,KAA4B,IAAK,CAChD;YACEpB,EAAE,EAAE,mBAAmB;YACvBb,KAAK,EAAEO,YAAY,CAACO,UAAU,CAACoB,gBAAgB,IAAI,mBAAmB;YACtElB,IAAI,EAAE,gBAAgB;YACtBC,KAAK,EAAEgB,KAAK,CAACf,MAAM,CAAC,CAACC,GAAG,EAAEgB,CAAC,KAAKhB,GAAG,GAAGgB,CAAC,CAAClB,KAAK,EAAE,CAAC;UAClD,CAAC,EACD,GAAGgB,KAAK,CAACX,GAAG,CAACc,MAAM,KAAK;YACtBvB,EAAE,EAAEuB,MAAM,CAACZ,IAAI;YACfxB,KAAK,EAAEoC,MAAM,CAACX,IAAI,CAACY,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACX,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC;YACjEL,IAAI,EAAE,uBAAuBU,kBAAkB,CAACU,MAAM,CAACX,IAAI,CAAC,EAAE;YAC9DR,KAAK,EAAEmB,MAAM,CAACG;UAChB,CAAC,CAAC,CAAC,CACJ;UACD;MACJ;MAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAGlC,QAAQ,EAAE,CAAC;MACtG,IAAI,CAAC8B,QAAQ,CAACK,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MAEzD,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI,IAAIF,MAAM;MAElC3C,QAAQ,CAACO,aAAa,CAACsC,IAAI,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkBnD,IAAI,QAAQ,EAAEmD,KAAK,CAAC;MACpD9C,QAAQ,CAAC,CAAC;QACRS,EAAE,EAAE,OAAO;QACXb,KAAK,EAAE,qBAAqB;QAC5BoD,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+C,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQtD,IAAI;MACV,KAAK,WAAW;QACd,OAAO,YAAY;MACrB,KAAK,cAAc;QACjB,OAAO,eAAe;MACxB,KAAK,eAAe;QAClB,OAAO,gBAAgB;MACzB;QACE,OAAO,GAAG;IACd;EACF,CAAC;EAED,MAAMuD,OAAO,gBACXzD,OAAA;IAAKI,SAAS,EAAE;AACpB;AACA;AACA,QAAQA,SAAS;AACjB,KAAM;IAAAsD,QAAA,gBAEA1D,OAAA,CAACL,IAAI;MACHgE,EAAE,EAAEH,cAAc,CAAC,CAAE;MACrBpD,SAAS,EAAC,8FAA8F;MACxGwD,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;MAAA;MAAAJ,QAAA,eAErC1D,OAAA;QAAMI,SAAS,EAAC,gCAAgC;QAAAsD,QAAA,EAAEvD;MAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAGPlE,OAAA;MACEI,SAAS,EAAC,0FAA0F;MACpGwD,OAAO,EAAGC,CAAC,IAAK;QACdA,CAAC,CAACC,eAAe,CAAC,CAAC;QACnB;MACF,CAAE;MAAAJ,QAAA,eAEF1D,OAAA,CAACJ,WAAW;QACVuE,IAAI,EAAE,EAAG;QACT/D,SAAS,EAAC;MAA6D;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACElE,OAAA,CAACH,QAAQ;IACP4D,OAAO,EAAEA,OAAQ;IACjBnD,KAAK,EAAEA,KAAM;IACbE,OAAO,EAAEA,OAAQ;IACjB4D,MAAM,EAAEzD,SAAU;IAClB0D,WAAW,EAAE,IAAK;IAClBC,YAAY,EAAE,IAAK;IACnBC,SAAS,EAAC,aAAa;IACvBnE,SAAS,EAAC,OAAO;IACjBoE,iBAAiB,EAAC,2BAA2B;IAC7CC,YAAY,EAAE,MAAMvE,IAAI;EAAa;IAAA6D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtC,CAAC;AAEN,CAAC;AAAC7D,EAAA,CA7JIJ,kBAAqD;EAAA,QAOhCH,WAAW;AAAA;AAAA4E,EAAA,GAPhCzE,kBAAqD;AA+J3D,eAAeA,kBAAkB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}