import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import ScholarshipCard from '../components/ScholarshipCard';
import ProfessionalPageLayout, { ProfessionalContentGrid } from '../components/ProfessionalPageLayout';
import sidebarService from '../services/sidebarService';

interface Scholarship {
  id: number;
  title: string;
  thumbnail?: string;
  deadline: string;
  isOpen: boolean;
  country: string;
  level?: string;
}

interface CountryStatistics {
  country: string;
  totalScholarships: number;
  openScholarships: number;
  closedScholarships: number;
  scholarshipsByLevel: Array<{
    level: string;
    count: number;
  }>;
}

const CountryDetail: React.FC = () => {
  const { country } = useParams<{ country: string }>();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [statistics, setStatistics] = useState<CountryStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const decodedCountry = country ? decodeURIComponent(country) : '';

  const fetchScholarships = React.useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12'
      });

      const response = await fetch(`/api/countries/${encodeURIComponent(decodedCountry)}/scholarships?${params}`);
      if (response.ok) {
        const data = await response.json();
        setScholarships(data.data || []);
        setTotalPages(data.pagination?.totalPages || 1);
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
    } finally {
      setLoading(false);
    }
  }, [decodedCountry, currentPage]);

  const fetchStatistics = React.useCallback(async () => {
    try {
      const response = await fetch(`/api/countries/${encodeURIComponent(decodedCountry)}/statistics`);
      if (response.ok) {
        const data = await response.json();
        setStatistics(data.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  }, [decodedCountry]);

  useEffect(() => {
    if (decodedCountry) {
      fetchScholarships();
      fetchStatistics();
    }
  }, [decodedCountry, fetchScholarships, fetchStatistics]);



  const handleScholarshipClick = (id: number) => {
    window.location.href = `/scholarships/${id}`;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderScholarshipItem = (scholarship: Scholarship) => (
    <ScholarshipCard
      key={scholarship.id}
      id={scholarship.id}
      title={scholarship.title}
      thumbnail={scholarship.thumbnail || ''}
      deadline={scholarship.deadline}
      isOpen={scholarship.isOpen}
      country={scholarship.country}
      onClick={handleScholarshipClick}
    />
  );

  return (
    <ProfessionalPageLayout
      hero={{
        title: decodedCountry,
        subtitle: "Découvrez toutes les bourses d'études disponibles",
        icon: sidebarService.getCountryFlag(decodedCountry),
        backgroundColor: 'bg-gradient-to-r from-blue-600 to-blue-800'
      }}
      statistics={statistics ? {
        total: statistics.totalScholarships,
        active: statistics.openScholarships,
        inactive: statistics.closedScholarships,
        label: 'Total des bourses',
        activeLabel: 'Bourses ouvertes',
        inactiveLabel: 'Bourses fermées'
      } : undefined}
      sidebarConfig={{
        type: 'countries',
        currentItem: decodedCountry,
        limit: 15
      }}
    >
      <ProfessionalContentGrid
        items={scholarships}
        loading={loading}
        emptyMessage="Aucune bourse n'est actuellement disponible pour ce pays."
        emptyIcon="📚"
        renderItem={renderScholarshipItem}
        pagination={{
          currentPage,
          totalPages,
          onPageChange: handlePageChange
        }}
      />
    </ProfessionalPageLayout>

  );
};

export default CountryDetail;
