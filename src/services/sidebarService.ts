/**
 * Professional Sidebar Data Service
 * Unified service for fetching sidebar data across all dedicated pages
 * Following industry standards like greatyop.com
 */

export interface SidebarData {
  relatedItems: any[];
  latestItems: any[];
  statistics?: any;
}

export interface SidebarConfig {
  type: 'countries' | 'levels' | 'opportunities';
  currentItem?: string;
  excludeId?: number;
  limit?: number;
}

class SidebarService {
  private baseUrl = '/api';
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly RELATED_ITEMS_TTL = 10 * 60 * 1000; // 10 minutes (changes less frequently)
  private readonly LATEST_ITEMS_TTL = 2 * 60 * 1000; // 2 minutes (changes more frequently)

  /**
   * Fetch unified sidebar data with intelligent caching and parallel requests
   */
  async fetchSidebarData(config: SidebarConfig): Promise<SidebarData> {
    try {
      // Create cache keys for different data types
      const relatedKey = this.getCacheKey('related', config);
      const latestKey = this.getCacheKey('latest', config);
      const statsKey = config.currentItem ? this.getCacheKey('stats', config) : null;

      // Check cache first, then fetch missing data in parallel
      const promises = [];

      // Related items (cached longer as they change less frequently)
      const cachedRelated = this.getFromCache(relatedKey);
      if (cachedRelated) {
        promises.push(Promise.resolve(cachedRelated));
      } else {
        promises.push(this.fetchRelatedItems(config).then(data => {
          this.setCache(relatedKey, data, this.RELATED_ITEMS_TTL);
          return data;
        }));
      }

      // Latest items (cached shorter as they change more frequently)
      const cachedLatest = this.getFromCache(latestKey);
      if (cachedLatest) {
        promises.push(Promise.resolve(cachedLatest));
      } else {
        promises.push(this.fetchLatestItems(config).then(data => {
          this.setCache(latestKey, data, this.LATEST_ITEMS_TTL);
          return data;
        }));
      }

      // Statistics (if needed)
      if (statsKey) {
        const cachedStats = this.getFromCache(statsKey);
        if (cachedStats) {
          promises.push(Promise.resolve(cachedStats));
        } else {
          promises.push(this.fetchStatistics(config).then(data => {
            this.setCache(statsKey, data, this.DEFAULT_TTL);
            return data;
          }));
        }
      }

      const results = await Promise.all(promises);
      const [relatedItems, latestItems, statistics] = results;

      return {
        relatedItems: relatedItems || [],
        latestItems: latestItems || [],
        statistics: statistics || null
      };
    } catch (error) {
      console.error('Error fetching sidebar data:', error);
      // Return cached data if available, otherwise empty data
      return this.getFallbackData(config);
    }
  }

  /**
   * Generate cache key for different data types
   */
  private getCacheKey(type: string, config: SidebarConfig): string {
    const parts = [type, config.type];
    if (config.currentItem) parts.push(config.currentItem);
    if (config.excludeId) parts.push(`exclude-${config.excludeId}`);
    if (config.limit) parts.push(`limit-${config.limit}`);
    return parts.join(':');
  }

  /**
   * Get data from cache if valid
   */
  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key); // Remove expired cache
    }
    return null;
  }

  /**
   * Set data in cache with TTL
   */
  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * Get fallback data from cache (even if expired) or return empty data
   */
  private getFallbackData(config: SidebarConfig): SidebarData {
    const relatedKey = this.getCacheKey('related', config);
    const latestKey = this.getCacheKey('latest', config);

    const fallbackRelated = this.cache.get(relatedKey)?.data || [];
    const fallbackLatest = this.cache.get(latestKey)?.data || [];

    return {
      relatedItems: fallbackRelated,
      latestItems: fallbackLatest
    };
  }

  /**
   * Fetch related items with retry logic and better error handling
   */
  private async fetchRelatedItems(config: SidebarConfig): Promise<any[]> {
    return this.fetchWithRetry(async () => {
      let endpoint = '';
      const params = new URLSearchParams();

      if (config.limit) {
        params.append('limit', config.limit.toString());
      }

      if (config.currentItem) {
        params.append(`exclude${this.getExcludeParam(config.type)}`, config.currentItem);
      }

      switch (config.type) {
        case 'countries':
          endpoint = `${this.baseUrl}/countries/sidebar`;
          break;
        case 'levels':
          endpoint = `${this.baseUrl}/scholarships/levels`;
          break;
        case 'opportunities':
          endpoint = `${this.baseUrl}/opportunities/types-sidebar`;
          break;
        default:
          throw new Error(`Unknown config type: ${config.type}`);
      }

      const response = await this.fetchWithTimeout(`${endpoint}?${params}`, 5000);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || [];
    }, `related-${config.type}`);
  }

  /**
   * Fetch latest items with enhanced error handling
   */
  private async fetchLatestItems(config: SidebarConfig): Promise<any[]> {
    return this.fetchWithRetry(async () => {
      const params = new URLSearchParams();
      params.append('limit', '6');

      if (config.excludeId) {
        params.append('excludeId', config.excludeId.toString());
      }

      let endpoint = '';
      if (config.type === 'opportunities') {
        endpoint = `${this.baseUrl}/opportunities/latest`;
      } else {
        endpoint = `${this.baseUrl}/scholarships/latest`;
      }

      const response = await this.fetchWithTimeout(`${endpoint}?${params}`, 5000);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || [];
    }, `latest-${config.type}`);
  }

  /**
   * Fetch with timeout to prevent hanging requests
   */
  private async fetchWithTimeout(url: string, timeout: number): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Retry logic for failed requests
   */
  private async fetchWithRetry<T>(
    fetchFn: () => Promise<T>,
    operation: string,
    maxRetries: number = 2
  ): Promise<T> {
    let lastError: Error = new Error('Unknown error');

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fetchFn();
      } catch (error) {
        lastError = error as Error;
        console.warn(`${operation} attempt ${attempt + 1} failed:`, error);

        if (attempt < maxRetries) {
          // Exponential backoff: 500ms, 1000ms, 2000ms
          const delay = 500 * Math.pow(2, attempt);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    console.error(`${operation} failed after ${maxRetries + 1} attempts:`, lastError);
    throw lastError;
  }

  /**
   * Fetch statistics with enhanced error handling
   */
  private async fetchStatistics(config: SidebarConfig): Promise<any> {
    if (!config.currentItem) return null;

    return this.fetchWithRetry(async () => {
      let endpoint = '';

      switch (config.type) {
        case 'countries':
          endpoint = `${this.baseUrl}/countries/${encodeURIComponent(config.currentItem!)}/statistics`;
          break;
        case 'levels':
          // Level statistics can be computed from the main data
          return null;
        case 'opportunities':
          // Opportunity type statistics can be computed from the main data
          return null;
      }

      if (endpoint) {
        const response = await this.fetchWithTimeout(endpoint, 5000);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        return data.data;
      }
      return null;
    }, `stats-${config.type}-${config.currentItem}`);
  }

  /**
   * Clear cache for specific type or all cache
   */
  public clearCache(type?: string): void {
    if (type) {
      // Clear cache entries that start with the type
      for (const [key] of this.cache) {
        if (key.startsWith(type)) {
          this.cache.delete(key);
        }
      }
    } else {
      // Clear all cache
      this.cache.clear();
    }
  }

  /**
   * Get cache statistics for debugging
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Preload data for better performance
   */
  public async preloadData(configs: SidebarConfig[]): Promise<void> {
    const promises = configs.map(config =>
      this.fetchSidebarData(config).catch(error => {
        console.warn('Preload failed for config:', config, error);
      })
    );

    await Promise.allSettled(promises);
  }

  /**
   * Get the correct exclude parameter name based on type
   */
  private getExcludeParam(type: string): string {
    switch (type) {
      case 'countries':
        return 'Country';
      case 'levels':
        return 'Level';
      case 'opportunities':
        return 'Type';
      default:
        return 'Item';
    }
  }

  /**
   * Get country flag emoji (utility function)
   */
  getCountryFlag(country: string): string {
    const flagMap: { [key: string]: string } = {
      'France': '🇫🇷',
      'Germany': '🇩🇪',
      'United Kingdom': '🇬🇧',
      'United States': '🇺🇸',
      'Canada': '🇨🇦',
      'Australia': '🇦🇺',
      'Netherlands': '🇳🇱',
      'Sweden': '🇸🇪',
      'Norway': '🇳🇴',
      'Denmark': '🇩🇰',
      'Switzerland': '🇨🇭',
      'Belgium': '🇧🇪',
      'Austria': '🇦🇹',
      'Italy': '🇮🇹',
      'Spain': '🇪🇸',
      'Japan': '🇯🇵',
      'South Korea': '🇰🇷',
      'Singapore': '🇸🇬',
      'New Zealand': '🇳🇿',
      'Finland': '🇫🇮'
    };
    return flagMap[country] || '🌍';
  }

  /**
   * Get level icon (utility function)
   */
  getLevelIcon(level: string): string {
    const iconMap: { [key: string]: string } = {
      'Bachelor': '🎓',
      'Master': '📚',
      'PhD': '🔬',
      'Postdoc': '👨‍🔬',
      'Undergraduate': '🎓',
      'Graduate': '📚',
      'Doctorate': '🔬'
    };
    return iconMap[level] || '📖';
  }

  /**
   * Get opportunity type icon (utility function)
   */
  getOpportunityIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'internship': '💼',
      'training': '🎯',
      'conference': '🎤',
      'workshop': '🛠️',
      'competition': '🏆'
    };
    return iconMap[type] || '🔗';
  }
}

export const sidebarService = new SidebarService();
export default sidebarService;
